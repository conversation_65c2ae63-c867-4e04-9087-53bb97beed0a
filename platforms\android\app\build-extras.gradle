android {
    compileSdkVersion 33
    buildToolsVersion "33.0.0"
    
    defaultConfig {
        applicationId "com.mahm.zodiac.calculator"
        minSdkVersion 22
        targetSdkVersion 33
        versionCode 1
        versionName "1.0.0"
        
        // دعم اللغة العربية والـ RTL
        resConfigs "ar", "en"
        
        // تحسينات الأداء
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
    }
    
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // إعدادات التوقيع (يجب إضافة keystore)
            // signingConfig signingConfigs.release
        }
        debug {
            debuggable true
            minifyEnabled false
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    // دعم اللغة العربية
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
    
    // تحسينات الذاكرة
    dexOptions {
        javaMaxHeapSize "4g"
    }
}
