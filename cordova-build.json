{"android": {"debug": {"buildType": "debug", "gradleArg": ["--no-daemon", "--parallel", "--max-workers=4"]}, "release": {"buildType": "release", "gradleArg": ["--no-daemon", "--parallel", "--max-workers=4"]}}, "buildConfig": {"android": {"debug": {"minSdkVersion": 22, "targetSdkVersion": 33, "compileSdkVersion": 33, "buildToolsVersion": "33.0.0"}, "release": {"minSdkVersion": 22, "targetSdkVersion": 33, "compileSdkVersion": 33, "buildToolsVersion": "33.0.0"}}}, "preferences": {"android": {"AndroidLaunchMode": "singleTop", "AndroidPersistentFileLocation": "Internal", "LoadUrlTimeoutValue": "60000", "SplashScreen": "screen", "SplashScreenDelay": "3000", "AutoHideSplashScreen": "true", "FadeSplashScreen": "true", "FadeSplashScreenDuration": "750", "ShowSplashScreenSpinner": "false", "Orientation": "portrait", "Fullscreen": "false", "BackgroundColor": "0xff667eea", "StatusBarOverlaysWebView": "false", "StatusBarBackgroundColor": "#667eea", "StatusBarStyle": "lightcontent"}}}