#!/bin/bash

# سكريبت بناء تطبيق حاسبة الأبراج لنظام أندرويد
# المطور: محمد طعم شريف الخفاجي

echo "🌟 بدء بناء تطبيق حاسبة الأبراج 🌟"
echo "========================================"

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً"
    exit 1
fi

# التحقق من وجود Cordova
if ! command -v cordova &> /dev/null; then
    echo "📦 تثبيت Cordova..."
    npm install -g cordova
fi

# التحقق من وجود Java
if ! command -v java &> /dev/null; then
    echo "❌ Java غير مثبت. يرجى تثبيت Java JDK 8 أو أحدث"
    exit 1
fi

# التحقق من وجود Android SDK
if [ -z "$ANDROID_HOME" ]; then
    echo "❌ متغير ANDROID_HOME غير محدد. يرجى تثبيت Android SDK"
    exit 1
fi

echo "✅ جميع المتطلبات متوفرة"
echo ""

# تثبيت التبعيات
echo "📦 تثبيت التبعيات..."
npm install

# إضافة منصة أندرويد
echo "🤖 إضافة منصة أندرويد..."
cordova platform add android

# إضافة الإضافات المطلوبة
echo "🔌 إضافة الإضافات..."
cordova plugin add cordova-plugin-whitelist
cordova plugin add cordova-plugin-statusbar
cordova plugin add cordova-plugin-device
cordova plugin add cordova-plugin-splashscreen
cordova plugin add cordova-plugin-network-information
cordova plugin add cordova-plugin-vibration
cordova plugin add cordova-plugin-dialogs

# التحضير للبناء
echo "🔧 تحضير المشروع..."
cordova prepare android

# بناء التطبيق للتطوير
echo "🏗️ بناء التطبيق (وضع التطوير)..."
cordova build android

# التحقق من نجاح البناء
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ تم بناء التطبيق بنجاح!"
    echo "📱 ملف APK متوفر في: platforms/android/app/build/outputs/apk/debug/"
    echo ""
    
    # عرض معلومات الملف
    APK_FILE="platforms/android/app/build/outputs/apk/debug/app-debug.apk"
    if [ -f "$APK_FILE" ]; then
        echo "📊 معلومات الملف:"
        echo "   📁 المسار: $APK_FILE"
        echo "   📏 الحجم: $(du -h "$APK_FILE" | cut -f1)"
        echo "   📅 التاريخ: $(date -r "$APK_FILE" '+%Y-%m-%d %H:%M:%S')"
    fi
    
    echo ""
    echo "🚀 لتشغيل التطبيق على جهاز متصل:"
    echo "   cordova run android"
    echo ""
    echo "📦 لبناء نسخة الإنتاج:"
    echo "   ./build-release.sh"
    
else
    echo ""
    echo "❌ فشل في بناء التطبيق"
    echo "🔍 تحقق من الأخطاء أعلاه وحاول مرة أخرى"
    exit 1
fi

echo ""
echo "🎉 انتهى بناء التطبيق بنجاح!"
echo "👨‍💻 المطور: محمد طعم شريف الخفاجي"
