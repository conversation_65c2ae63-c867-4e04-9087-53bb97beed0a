#!/bin/bash

# سكريبت شامل لبناء واستخراج ملفات APK
# المطور: محمد طعم شريف الخفاجي

echo "🚀 بناء واستخراج تطبيق حاسبة الأبراج 🚀"
echo "=========================================="

# اختيار نوع البناء
echo "📋 اختر نوع البناء:"
echo "   1) نسخة التطوير (Debug) - للاختبار"
echo "   2) نسخة الإنتاج (Release) - للنشر"
echo "   3) كلاهما"
echo ""

read -p "🔢 أدخل اختيارك (1-3): " BUILD_CHOICE

case $BUILD_CHOICE in
    1)
        BUILD_TYPE="debug"
        echo "🧪 سيتم بناء نسخة التطوير"
        ;;
    2)
        BUILD_TYPE="release"
        echo "🏭 سيتم بناء نسخة الإنتاج"
        ;;
    3)
        BUILD_TYPE="both"
        echo "🔄 سيتم بناء كلا النسختين"
        ;;
    *)
        echo "❌ اختيار غير صحيح. سيتم بناء نسخة التطوير افتراضياً"
        BUILD_TYPE="debug"
        ;;
esac

echo ""
echo "⏱️ بدء عملية البناء..."
echo ""

# بناء نسخة التطوير
if [ "$BUILD_TYPE" = "debug" ] || [ "$BUILD_TYPE" = "both" ]; then
    echo "🧪 بناء نسخة التطوير..."
    echo "========================="
    
    if [ -f "build.sh" ]; then
        chmod +x build.sh
        ./build.sh
        
        if [ $? -eq 0 ]; then
            echo "✅ تم بناء نسخة التطوير بنجاح"
        else
            echo "❌ فشل في بناء نسخة التطوير"
            exit 1
        fi
    else
        echo "❌ ملف build.sh غير موجود"
        exit 1
    fi
    
    echo ""
fi

# بناء نسخة الإنتاج
if [ "$BUILD_TYPE" = "release" ] || [ "$BUILD_TYPE" = "both" ]; then
    echo "🏭 بناء نسخة الإنتاج..."
    echo "======================="
    
    if [ -f "build-release.sh" ]; then
        chmod +x build-release.sh
        ./build-release.sh
        
        if [ $? -eq 0 ]; then
            echo "✅ تم بناء نسخة الإنتاج بنجاح"
        else
            echo "❌ فشل في بناء نسخة الإنتاج"
            exit 1
        fi
    else
        echo "❌ ملف build-release.sh غير موجود"
        exit 1
    fi
    
    echo ""
fi

# استخراج ملفات APK
echo "📱 استخراج ملفات APK..."
echo "======================="

if [ -f "extract-apk.sh" ]; then
    chmod +x extract-apk.sh
    ./extract-apk.sh
    
    if [ $? -eq 0 ]; then
        echo "✅ تم استخراج ملفات APK بنجاح"
    else
        echo "❌ فشل في استخراج ملفات APK"
        exit 1
    fi
else
    echo "❌ ملف extract-apk.sh غير موجود"
    exit 1
fi

echo ""
echo "🎉 تمت العملية بنجاح!"
echo "===================="

# عرض النتائج النهائية
OUTPUT_DIR="APK_OUTPUT"

if [ -d "$OUTPUT_DIR" ]; then
    echo ""
    echo "📁 ملفات APK الجاهزة في مجلد: $OUTPUT_DIR"
    echo ""
    
    # عرض محتويات المجلد
    echo "📋 محتويات المجلد:"
    ls -la "$OUTPUT_DIR"/*.apk 2>/dev/null | while read line; do
        echo "   $line"
    done
    
    echo ""
    echo "📊 أحجام الملفات:"
    for apk_file in "$OUTPUT_DIR"/*.apk; do
        if [ -f "$apk_file" ]; then
            filename=$(basename "$apk_file")
            size=$(du -h "$apk_file" | cut -f1)
            echo "   📱 $filename - $size"
        fi
    done
    
    echo ""
    echo "🚀 خطوات التثبيت:"
    echo "   1. انسخ مجلد $OUTPUT_DIR إلى جهازك"
    echo "   2. انقل ملف APK إلى جهاز أندرويد"
    echo "   3. فعّل 'مصادر غير معروفة' في الإعدادات"
    echo "   4. اضغط على ملف APK لتثبيته"
    
    echo ""
    echo "💻 أو استخدم ADB:"
    for apk_file in "$OUTPUT_DIR"/*.apk; do
        if [ -f "$apk_file" ]; then
            filename=$(basename "$apk_file")
            echo "   adb install $OUTPUT_DIR/$filename"
            break  # عرض مثال واحد فقط
        fi
    done
    
    echo ""
    echo "📤 للمشاركة:"
    echo "   - ارفع مجلد $OUTPUT_DIR إلى Google Drive"
    echo "   - أو أرسل ملف APK عبر البريد الإلكتروني"
    echo "   - أو انسخه إلى USB"
    
else
    echo "❌ مجلد الإخراج غير موجود"
fi

echo ""
echo "📋 ملخص العملية:"
echo "=================="

if [ "$BUILD_TYPE" = "debug" ]; then
    echo "✅ تم بناء نسخة التطوير"
elif [ "$BUILD_TYPE" = "release" ]; then
    echo "✅ تم بناء نسخة الإنتاج"
else
    echo "✅ تم بناء كلا النسختين"
fi

echo "✅ تم استخراج ملفات APK"
echo "✅ تم إنشاء ملفات المعلومات"

echo ""
echo "🎯 الخطوات التالية:"
echo "   📱 اختبر التطبيق على جهازك"
echo "   🎨 أنشئ الأصول البصرية (الأيقونات)"
echo "   🏪 انشر على Google Play Store"

echo ""
echo "👨‍💻 المطور: محمد طعم شريف الخفاجي"
echo "📅 تاريخ البناء: $(date)"
echo ""
echo "🎊 مبروك! تطبيقك جاهز للاستخدام! 🎊"
