#!/bin/bash

# سكريبت استخراج وتنظيم ملفات APK
# المطور: محمد طعم شريف الخفاجي

echo "📱 استخراج ملفات APK لتطبيق حاسبة الأبراج 📱"
echo "=============================================="

# المتغيرات
APP_NAME="zodiac-calculator"
VERSION="v1.0.0"
DATE=$(date +"%Y%m%d_%H%M")
DEVELOPER="محمد-طعم-شريف-الخفاجي"

# مسارات الملفات
DEBUG_APK_SOURCE="platforms/android/app/build/outputs/apk/debug/app-debug.apk"
RELEASE_APK_SOURCE="platforms/android/app/build/outputs/apk/release/app-release.apk"

# إنشاء مجلد الإخراج
OUTPUT_DIR="APK_OUTPUT"
mkdir -p "$OUTPUT_DIR"

echo "📁 إنشاء مجلد الإخراج: $OUTPUT_DIR"

# التحقق من وجود ملف Debug APK
if [ -f "$DEBUG_APK_SOURCE" ]; then
    DEBUG_APK_NAME="${APP_NAME}-${VERSION}-debug-${DATE}.apk"
    cp "$DEBUG_APK_SOURCE" "$OUTPUT_DIR/$DEBUG_APK_NAME"
    
    # نسخة مبسطة للاستخدام السريع
    SIMPLE_DEBUG_NAME="${APP_NAME}-debug.apk"
    cp "$DEBUG_APK_SOURCE" "$OUTPUT_DIR/$SIMPLE_DEBUG_NAME"
    
    echo "✅ تم استخراج نسخة التطوير:"
    echo "   📁 $OUTPUT_DIR/$DEBUG_APK_NAME"
    echo "   📁 $OUTPUT_DIR/$SIMPLE_DEBUG_NAME"
    echo "   📏 الحجم: $(du -h "$OUTPUT_DIR/$DEBUG_APK_NAME" | cut -f1)"
    
    DEBUG_EXISTS=true
else
    echo "⚠️ ملف Debug APK غير موجود"
    echo "💡 قم ببناء التطبيق أولاً: ./build.sh"
    DEBUG_EXISTS=false
fi

echo ""

# التحقق من وجود ملف Release APK
if [ -f "$RELEASE_APK_SOURCE" ]; then
    RELEASE_APK_NAME="${APP_NAME}-${VERSION}-release-${DATE}.apk"
    cp "$RELEASE_APK_SOURCE" "$OUTPUT_DIR/$RELEASE_APK_NAME"
    
    # نسخة مبسطة للنشر
    SIMPLE_RELEASE_NAME="${APP_NAME}-release.apk"
    cp "$RELEASE_APK_SOURCE" "$OUTPUT_DIR/$SIMPLE_RELEASE_NAME"
    
    echo "✅ تم استخراج نسخة الإنتاج:"
    echo "   📁 $OUTPUT_DIR/$RELEASE_APK_NAME"
    echo "   📁 $OUTPUT_DIR/$SIMPLE_RELEASE_NAME"
    echo "   📏 الحجم: $(du -h "$OUTPUT_DIR/$RELEASE_APK_NAME" | cut -f1)"
    
    RELEASE_EXISTS=true
else
    echo "⚠️ ملف Release APK غير موجود"
    echo "💡 قم ببناء نسخة الإنتاج: ./build-release.sh"
    RELEASE_EXISTS=false
fi

echo ""

# إنشاء ملف معلومات مرافق
INFO_FILE="$OUTPUT_DIR/معلومات-التطبيق.txt"
cat > "$INFO_FILE" << EOF
🌟 تطبيق حاسبة الأبراج 🌟
============================

📱 معلومات التطبيق:
   الاسم: حاسبة الأبراج
   المعرف: com.mahm.zodiac.calculator
   الإصدار: 1.0.0
   المطور: محمد طعم شريف الخفاجي

📅 تاريخ الإنشاء: $(date)

🤖 متطلبات النظام:
   أندرويد: 5.1+ (API 22)
   المساحة المطلوبة: 15 MB
   الإنترنت: مطلوب للتوقعات اليومية

📋 طريقة التثبيت:
   1. فعّل "مصادر غير معروفة" في إعدادات الأمان
   2. اضغط على ملف APK
   3. اتبع تعليمات التثبيت

✨ المميزات الرئيسية:
   🎂 حساب العمر الدقيق بالسنوات والأشهر والأيام
   🌙 تحويل تاريخ الميلاد للتقويم الهجري
   ⭐ معلومات شاملة عن جميع الأبراج الـ12
   🔮 أكثر من 180 توقع يومي متجدد
   💼 اقتراحات المهن المناسبة لكل برج
   🍀 الأرقام المحظوظة لكل برج
   💕 توافق الأبراج مع بعضها البعض
   💡 نصائح يومية مخصصة

🎨 مميزات التصميم:
   - واجهة عربية أصيلة مع دعم RTL
   - ألوان متدرجة جميلة (أزرق → بنفسجي)
   - تصميم متجاوب لجميع أحجام الشاشات
   - خطوط عربية أنيقة (Cairo)
   - تأثيرات بصرية متقدمة

🔒 الأمان والخصوصية:
   - التطبيق آمن 100%
   - لا يجمع بيانات شخصية
   - لا يحتوي على إعلانات
   - مفتوح المصدر

📞 الدعم التقني:
   في حالة مواجهة أي مشاكل، تأكد من:
   - إصدار أندرويد 5.1 أو أحدث
   - توفر مساحة كافية (15 MB)
   - تفعيل "مصادر غير معروفة"

🎉 استمتع بالتطبيق!

---
👨‍💻 المطور: محمد طعم شريف الخفاجي
📅 تاريخ الإنشاء: $(date)
🌟 الإصدار: 1.0.0
EOF

echo "📄 تم إنشاء ملف المعلومات: $INFO_FILE"

# إنشاء ملف README للمجلد
README_FILE="$OUTPUT_DIR/README.md"
cat > "$README_FILE" << EOF
# 📱 ملفات APK - تطبيق حاسبة الأبراج

## 📁 محتويات المجلد

EOF

if [ "$DEBUG_EXISTS" = true ]; then
cat >> "$README_FILE" << EOF
### 🧪 نسخة التطوير (Debug):
- \`$SIMPLE_DEBUG_NAME\` - للاختبار والتطوير
- \`$DEBUG_APK_NAME\` - نسخة مؤرخة

EOF
fi

if [ "$RELEASE_EXISTS" = true ]; then
cat >> "$README_FILE" << EOF
### 🏭 نسخة الإنتاج (Release):
- \`$SIMPLE_RELEASE_NAME\` - للنشر والتوزيع
- \`$RELEASE_APK_NAME\` - نسخة مؤرخة موقعة

EOF
fi

cat >> "$README_FILE" << EOF
### 📄 ملفات إضافية:
- \`معلومات-التطبيق.txt\` - معلومات مفصلة عن التطبيق
- \`README.md\` - هذا الملف

## 🚀 طريقة التثبيت

### على الجهاز مباشرة:
1. انقل ملف APK إلى جهاز أندرويد
2. فعّل "مصادر غير معروفة" في الإعدادات
3. اضغط على ملف APK واتبع التعليمات

### باستخدام ADB:
\`\`\`bash
adb install $SIMPLE_DEBUG_NAME
\`\`\`

## 📊 معلومات التطبيق

- **الاسم**: حاسبة الأبراج
- **المعرف**: com.mahm.zodiac.calculator
- **الإصدار**: 1.0.0
- **الحجم**: ~8-12 MB
- **أندرويد**: 5.1+ (API 22)
- **المطور**: محمد طعم شريف الخفاجي

## 🌟 المميزات

- 🎂 حساب العمر الدقيق
- 🌙 التحويل الهجري
- ⭐ معلومات الأبراج الشاملة
- 🔮 180+ توقع يومي
- 💼 المهن المناسبة
- 🍀 الأرقام المحظوظة
- 💕 توافق الأبراج

---

📅 تاريخ الإنشاء: $(date)
👨‍💻 المطور: محمد طعم شريف الخفاجي
EOF

echo "📄 تم إنشاء ملف README: $README_FILE"

# عرض ملخص النتائج
echo ""
echo "📊 ملخص ملفات APK المستخرجة:"
echo "================================="

if [ "$DEBUG_EXISTS" = true ] || [ "$RELEASE_EXISTS" = true ]; then
    echo "📁 مجلد الإخراج: $OUTPUT_DIR/"
    echo ""
    
    if [ "$DEBUG_EXISTS" = true ]; then
        echo "🧪 نسخة التطوير:"
        echo "   📱 $SIMPLE_DEBUG_NAME"
        echo "   📱 $DEBUG_APK_NAME"
        echo "   📏 الحجم: $(du -h "$OUTPUT_DIR/$SIMPLE_DEBUG_NAME" | cut -f1)"
        echo "   🎯 الاستخدام: للاختبار والتطوير"
        echo ""
    fi
    
    if [ "$RELEASE_EXISTS" = true ]; then
        echo "🏭 نسخة الإنتاج:"
        echo "   📱 $SIMPLE_RELEASE_NAME"
        echo "   📱 $RELEASE_APK_NAME"
        echo "   📏 الحجم: $(du -h "$OUTPUT_DIR/$SIMPLE_RELEASE_NAME" | cut -f1)"
        echo "   🎯 الاستخدام: للنشر على Google Play"
        echo ""
    fi
    
    echo "📄 ملفات إضافية:"
    echo "   📋 معلومات-التطبيق.txt"
    echo "   📖 README.md"
    echo ""
    
    echo "🎉 تم استخراج ملفات APK بنجاح!"
    echo ""
    echo "📱 لتثبيت التطبيق:"
    if [ "$DEBUG_EXISTS" = true ]; then
        echo "   adb install $OUTPUT_DIR/$SIMPLE_DEBUG_NAME"
    fi
    echo ""
    echo "📤 لمشاركة التطبيق:"
    echo "   انسخ مجلد $OUTPUT_DIR كاملاً"
    
else
    echo "❌ لم يتم العثور على أي ملفات APK"
    echo ""
    echo "💡 لإنشاء ملفات APK:"
    echo "   🧪 للتطوير: ./build.sh"
    echo "   🏭 للإنتاج: ./build-release.sh"
fi

echo ""
echo "👨‍💻 المطور: محمد طعم شريف الخفاجي"
echo "📅 تاريخ الاستخراج: $(date)"
