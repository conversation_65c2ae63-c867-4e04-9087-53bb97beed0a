#!/bin/bash

# سكريبت إنشاء قوالب الأصول البصرية
# المطور: محمد طعم شريف الخفاجي

echo "🎨 إنشاء قوالب الأصول البصرية 🎨"
echo "===================================="

# إنشاء مجلدات التنظيم
echo "📁 إنشاء مجلدات التنظيم..."
mkdir -p assets/{icons,screenshots,splash,marketing}
mkdir -p assets/icons/{android,ios,web}
mkdir -p assets/screenshots/{phone,tablet}
mkdir -p assets/splash/{android,ios}
mkdir -p assets/marketing/{banners,posters,social}

echo "✅ تم إنشاء مجلدات التنظيم"

# إنشاء ملف قائمة الأصول المطلوبة
echo "📋 إنشاء قائمة الأصول المطلوبة..."

cat > assets/ASSETS_CHECKLIST.md << 'EOF'
# 📋 قائمة الأصول البصرية المطلوبة

## 🖼️ الأيقونات

### أندرويد:
- [ ] **أيقونة المتجر**: 512x512 بكسل (PNG)
- [ ] **ldpi**: 36x36 بكسل
- [ ] **mdpi**: 48x48 بكسل
- [ ] **hdpi**: 72x72 بكسل
- [ ] **xhdpi**: 96x96 بكسل
- [ ] **xxhdpi**: 144x144 بكسل
- [ ] **xxxhdpi**: 192x192 بكسل
- [ ] **Adaptive Icon**: 108x108 بكسل (foreground + background)

### الويب:
- [ ] **Favicon**: 16x16, 32x32, 48x48 بكسل
- [ ] **Apple Touch Icon**: 180x180 بكسل
- [ ] **Web App Icon**: 192x192, 512x512 بكسل

## 📱 لقطات الشاشة

### الهاتف (1080x1920 أو 9:16):
- [ ] **الشاشة الرئيسية**: واجهة البداية
- [ ] **إدخال التاريخ**: التقويم مفتوح
- [ ] **نتائج العمر**: العمر والبرج
- [ ] **التوقعات اليومية**: التوقع المفصل
- [ ] **الصفات والمهن**: المعلومات الإضافية
- [ ] **الأرقام المحظوظة**: الأرقام الذهبية
- [ ] **توافق الأبراج**: العلاقات العاطفية

### التابلت (1200x1920 أو 10:16):
- [ ] **نظرة عامة**: شاشة كاملة
- [ ] **التفاصيل**: معلومات مفصلة

## 🌟 شاشات البداية

### أندرويد:
- [ ] **ldpi**: 200x320 بكسل
- [ ] **mdpi**: 320x480 بكسل
- [ ] **hdpi**: 480x800 بكسل
- [ ] **xhdpi**: 720x1280 بكسل
- [ ] **xxhdpi**: 960x1600 بكسل
- [ ] **xxxhdpi**: 1280x1920 بكسل

## 📊 مواد تسويقية

### بانرات وسائل التواصل:
- [ ] **فيسبوك**: 1200x630 بكسل
- [ ] **تويتر**: 1024x512 بكسل
- [ ] **إنستغرام**: 1080x1080 بكسل
- [ ] **لينكد إن**: 1200x627 بكسل
- [ ] **يوتيوب**: 1280x720 بكسل

### بوسترات:
- [ ] **بوستر A4**: 2480x3508 بكسل (300 DPI)
- [ ] **بوستر رقمي**: 1080x1350 بكسل
- [ ] **بانر ويب**: 728x90, 300x250 بكسل

### فيديو:
- [ ] **فيديو ترويجي**: 1920x1080 (30 ثانية)
- [ ] **GIF متحرك**: 800x600 (5-10 ثوان)

## 🎨 مواصفات التصميم

### الألوان الأساسية:
- **الأساسي**: #667eea (أزرق)
- **الثانوي**: #764ba2 (بنفسجي)
- **الذهبي**: #FFD700 (للنجوم)
- **الأبيض**: #FFFFFF (للنصوص)
- **الرمادي**: #666666 (للنصوص الثانوية)

### الخطوط:
- **العربي**: Cairo (Regular, Bold)
- **الإنجليزي**: Roboto (Regular, Bold)

### رموز الأبراج:
♈ ♉ ♊ ♋ ♌ ♍ ♎ ♏ ♐ ♑ ♒ ♓

## 📝 ملاحظات مهمة

1. **الجودة**: استخدم دقة عالية (300 DPI للطباعة، 72 DPI للويب)
2. **التنسيق**: PNG للشفافية، JPEG للصور الفوتوغرافية
3. **الألوان**: استخدم نفس لوحة ألوان التطبيق
4. **النصوص**: تأكد من وضوح النصوص العربية
5. **الاتساق**: حافظ على نفس الأسلوب في جميع الأصول

## 🔗 أدوات مفيدة

- **Canva**: https://www.canva.com/
- **App Icon Generator**: https://appicon.co/
- **Device Art Generator**: https://developer.android.com/distribute/marketing-tools/device-art-generator
- **GIMP**: https://www.gimp.org/
- **Figma**: https://www.figma.com/

---

📅 تاريخ الإنشاء: $(date)
👨‍💻 المطور: محمد طعم شريف الخفاجي
EOF

# إنشاء قوالب HTML للمعاينة
echo "🌐 إنشاء قوالب HTML للمعاينة..."

cat > assets/preview.html << 'EOF'
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة أصول تطبيق حاسبة الأبراج</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #667eea;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px dashed #667eea;
            border-radius: 10px;
        }
        .section h2 {
            color: #764ba2;
            border-bottom: 2px solid #764ba2;
            padding-bottom: 10px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .asset-placeholder {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f9f9f9;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .asset-placeholder h3 {
            margin: 0 0 10px 0;
            color: #667eea;
        }
        .asset-placeholder p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .pending {
            background: #fff3cd;
            color: #856404;
        }
        .completed {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 معاينة أصول تطبيق حاسبة الأبراج</h1>
        
        <div class="section">
            <h2>🖼️ الأيقونات</h2>
            <div class="grid">
                <div class="asset-placeholder">
                    <h3>أيقونة المتجر</h3>
                    <p>512x512 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
                <div class="asset-placeholder">
                    <h3>أيقونات أندرويد</h3>
                    <p>6 أحجام مختلفة</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
                <div class="asset-placeholder">
                    <h3>Adaptive Icon</h3>
                    <p>108x108 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📱 لقطات الشاشة</h2>
            <div class="grid">
                <div class="asset-placeholder">
                    <h3>الشاشة الرئيسية</h3>
                    <p>1080x1920 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
                <div class="asset-placeholder">
                    <h3>نتائج العمر</h3>
                    <p>1080x1920 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
                <div class="asset-placeholder">
                    <h3>التوقعات اليومية</h3>
                    <p>1080x1920 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
                <div class="asset-placeholder">
                    <h3>الصفات والمهن</h3>
                    <p>1080x1920 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🌟 شاشات البداية</h2>
            <div class="grid">
                <div class="asset-placeholder">
                    <h3>شاشة البداية</h3>
                    <p>6 أحجام مختلفة</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 مواد تسويقية</h2>
            <div class="grid">
                <div class="asset-placeholder">
                    <h3>بانر فيسبوك</h3>
                    <p>1200x630 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
                <div class="asset-placeholder">
                    <h3>بوستر إنستغرام</h3>
                    <p>1080x1080 بكسل</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
                <div class="asset-placeholder">
                    <h3>بوستر ترويجي</h3>
                    <p>A4 - 300 DPI</p>
                    <span class="status pending">قيد الإنشاء</span>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3>📝 ملاحظات</h3>
            <p>هذه معاينة للأصول المطلوبة. قم بإنشاء كل أصل ووضعه في المجلد المناسب.</p>
            <p>استخدم الألوان والخطوط المحددة في دليل التصميم.</p>
            <p><strong>👨‍💻 المطور:</strong> محمد طعم شريف الخفاجي</p>
        </div>
    </div>
</body>
</html>
EOF

# إنشاء ملف README للأصول
cat > assets/README.md << 'EOF'
# 🎨 مجلد الأصول البصرية

هذا المجلد يحتوي على جميع الأصول البصرية المطلوبة لتطبيق حاسبة الأبراج.

## 📁 هيكل المجلدات

```
assets/
├── icons/           # الأيقونات
│   ├── android/     # أيقونات أندرويد
│   ├── ios/         # أيقونات iOS (مستقبلية)
│   └── web/         # أيقونات الويب
├── screenshots/     # لقطات الشاشة
│   ├── phone/       # لقطات الهاتف
│   └── tablet/      # لقطات التابلت
├── splash/          # شاشات البداية
│   ├── android/     # شاشات أندرويد
│   └── ios/         # شاشات iOS (مستقبلية)
└── marketing/       # مواد تسويقية
    ├── banners/     # بانرات
    ├── posters/     # بوسترات
    └── social/      # وسائل التواصل
```

## 🎯 الخطوات التالية

1. راجع ملف `ASSETS_CHECKLIST.md` للقائمة الكاملة
2. افتح `preview.html` لمعاينة الأصول
3. ابدأ بإنشاء الأيقونة الرئيسية
4. التقط لقطات الشاشة
5. أنشئ شاشات البداية
6. صمم المواد التسويقية

## 🛠️ أدوات مقترحة

- **Canva**: للتصميم السريع
- **GIMP**: للتحرير المتقدم
- **App Icon Generator**: لإنشاء جميع أحجام الأيقونات

---

📅 تاريخ الإنشاء: $(date)
👨‍💻 المطور: محمد طعم شريف الخفاجي
EOF

# إنشاء ملف CSS للألوان والخطوط
cat > assets/design-system.css << 'EOF'
/* نظام التصميم لتطبيق حاسبة الأبراج */

:root {
    /* الألوان الأساسية */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #FFD700;
    --text-primary: #FFFFFF;
    --text-secondary: #666666;
    --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* الخطوط */
    --font-arabic: 'Cairo', Arial, sans-serif;
    --font-english: 'Roboto', Arial, sans-serif;
    
    /* الأحجام */
    --border-radius: 15px;
    --shadow: 0 10px 30px rgba(0,0,0,0.2);
    --spacing: 20px;
}

/* الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

/* الأنماط الأساسية */
.primary-gradient {
    background: var(--background-gradient);
}

.primary-color {
    color: var(--primary-color);
}

.secondary-color {
    color: var(--secondary-color);
}

.accent-color {
    color: var(--accent-color);
}

.arabic-text {
    font-family: var(--font-arabic);
    direction: rtl;
}

.english-text {
    font-family: var(--font-english);
    direction: ltr;
}

/* رموز الأبراج */
.zodiac-symbols {
    font-size: 24px;
    color: var(--accent-color);
}

/* الأزرار */
.btn-primary {
    background: var(--background-gradient);
    color: var(--text-primary);
    border: none;
    border-radius: var(--border-radius);
    padding: 12px 24px;
    font-family: var(--font-arabic);
    font-weight: 700;
    box-shadow: var(--shadow);
}

/* البطاقات */
.card {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing);
    box-shadow: var(--shadow);
    margin: var(--spacing);
}
EOF

echo "✅ تم إنشاء جميع القوالب والملفات"
echo ""
echo "📁 تم إنشاء المجلدات التالية:"
echo "   assets/icons/android/"
echo "   assets/screenshots/phone/"
echo "   assets/splash/android/"
echo "   assets/marketing/banners/"
echo ""
echo "📄 تم إنشاء الملفات التالية:"
echo "   assets/ASSETS_CHECKLIST.md - قائمة الأصول المطلوبة"
echo "   assets/preview.html - معاينة الأصول"
echo "   assets/README.md - دليل المجلد"
echo "   assets/design-system.css - نظام التصميم"
echo ""
echo "🎯 الخطوات التالية:"
echo "   1. افتح assets/preview.html في المتصفح"
echo "   2. راجع assets/ASSETS_CHECKLIST.md"
echo "   3. ابدأ بإنشاء الأيقونة الرئيسية"
echo "   4. التقط لقطات الشاشة"
echo ""
echo "🎉 قوالب الأصول البصرية جاهزة!"
