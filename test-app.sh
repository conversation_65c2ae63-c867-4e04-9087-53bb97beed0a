#!/bin/bash

# سكريبت اختبار تطبيق حاسبة الأبراج
# المطور: محمد طعم شريف الخفاجي

echo "🧪 بدء اختبار تطبيق حاسبة الأبراج 🧪"
echo "========================================"

# المتغيرات
APP_PACKAGE="com.mahm.zodiac.calculator"
APK_FILE="zodiac-calculator-debug.apk"
TEST_RESULTS="test-results.txt"

# إنشاء ملف النتائج
echo "📋 تقرير اختبار تطبيق حاسبة الأبراج" > $TEST_RESULTS
echo "=========================================" >> $TEST_RESULTS
echo "📅 تاريخ الاختبار: $(date)" >> $TEST_RESULTS
echo "👨‍💻 المختبر: محمد طعم شريف الخفاجي" >> $TEST_RESULTS
echo "" >> $TEST_RESULTS

# التحقق من المتطلبات
echo "🔍 التحقق من المتطلبات..."

# التحقق من ADB
if ! command -v adb &> /dev/null; then
    echo "❌ ADB غير مثبت. يرجى تثبيت Android SDK"
    exit 1
fi

# التحقق من وجود ملف APK
if [ ! -f "$APK_FILE" ]; then
    echo "❌ ملف APK غير موجود: $APK_FILE"
    echo "💡 قم ببناء التطبيق أولاً باستخدام: ./build.sh"
    exit 1
fi

# التحقق من الأجهزة المتصلة
DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
if [ $DEVICES -eq 0 ]; then
    echo "❌ لا توجد أجهزة متصلة"
    echo "💡 تأكد من:"
    echo "   1. توصيل الجهاز عبر USB"
    echo "   2. تفعيل تصحيح USB"
    echo "   3. السماح للكمبيوتر بالوصول"
    exit 1
fi

echo "✅ جميع المتطلبات متوفرة"
echo "📱 عدد الأجهزة المتصلة: $DEVICES"

# عرض معلومات الأجهزة
echo ""
echo "📱 الأجهزة المتصلة:"
adb devices -l

# اختيار الجهاز (إذا كان هناك أكثر من جهاز)
if [ $DEVICES -gt 1 ]; then
    echo ""
    echo "⚠️ يوجد أكثر من جهاز متصل"
    echo "💡 سيتم الاختبار على الجهاز الأول"
fi

# بدء الاختبارات
echo ""
echo "🚀 بدء الاختبارات..."
echo ""

# اختبار 1: تثبيت التطبيق
echo "📦 اختبار 1: تثبيت التطبيق..."
adb install -r $APK_FILE > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ تم تثبيت التطبيق بنجاح"
    echo "✅ اختبار التثبيت: نجح" >> $TEST_RESULTS
else
    echo "❌ فشل في تثبيت التطبيق"
    echo "❌ اختبار التثبيت: فشل" >> $TEST_RESULTS
    exit 1
fi

# اختبار 2: التحقق من وجود التطبيق
echo "🔍 اختبار 2: التحقق من وجود التطبيق..."
PACKAGE_EXISTS=$(adb shell pm list packages | grep $APP_PACKAGE)

if [ ! -z "$PACKAGE_EXISTS" ]; then
    echo "✅ التطبيق موجود في النظام"
    echo "✅ اختبار وجود التطبيق: نجح" >> $TEST_RESULTS
else
    echo "❌ التطبيق غير موجود في النظام"
    echo "❌ اختبار وجود التطبيق: فشل" >> $TEST_RESULTS
fi

# اختبار 3: تشغيل التطبيق
echo "🚀 اختبار 3: تشغيل التطبيق..."
adb shell am start -n $APP_PACKAGE/.MainActivity > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ تم تشغيل التطبيق بنجاح"
    echo "✅ اختبار تشغيل التطبيق: نجح" >> $TEST_RESULTS
    sleep 3
else
    echo "❌ فشل في تشغيل التطبيق"
    echo "❌ اختبار تشغيل التطبيق: فشل" >> $TEST_RESULTS
fi

# اختبار 4: فحص الأخطاء
echo "🔍 اختبار 4: فحص الأخطاء..."
ERRORS=$(adb logcat -d | grep -i "error\|exception\|crash" | grep $APP_PACKAGE | wc -l)

if [ $ERRORS -eq 0 ]; then
    echo "✅ لا توجد أخطاء في السجل"
    echo "✅ اختبار الأخطاء: نجح (0 أخطاء)" >> $TEST_RESULTS
else
    echo "⚠️ تم العثور على $ERRORS خطأ في السجل"
    echo "⚠️ اختبار الأخطاء: تحذير ($ERRORS أخطاء)" >> $TEST_RESULTS
fi

# اختبار 5: فحص الذاكرة
echo "💾 اختبار 5: فحص استهلاك الذاكرة..."
MEMORY_INFO=$(adb shell dumpsys meminfo $APP_PACKAGE | grep "TOTAL" | head -1)

if [ ! -z "$MEMORY_INFO" ]; then
    echo "✅ معلومات الذاكرة متوفرة"
    echo "📊 $MEMORY_INFO"
    echo "✅ اختبار الذاكرة: نجح" >> $TEST_RESULTS
    echo "📊 استهلاك الذاكرة: $MEMORY_INFO" >> $TEST_RESULTS
else
    echo "⚠️ لا يمكن الحصول على معلومات الذاكرة"
    echo "⚠️ اختبار الذاكرة: تحذير" >> $TEST_RESULTS
fi

# اختبار 6: فحص الصلاحيات
echo "🔒 اختبار 6: فحص الصلاحيات..."
PERMISSIONS=$(adb shell dumpsys package $APP_PACKAGE | grep "permission" | wc -l)

echo "✅ عدد الصلاحيات المطلوبة: $PERMISSIONS"
echo "✅ اختبار الصلاحيات: نجح ($PERMISSIONS صلاحيات)" >> $TEST_RESULTS

# اختبار 7: اختبار الاستجابة
echo "⚡ اختبار 7: اختبار الاستجابة..."
echo "💡 يرجى اختبار التطبيق يدوياً للتأكد من:"
echo "   - سرعة الاستجابة"
echo "   - صحة الحسابات"
echo "   - جودة التصميم"
echo "   - عمل جميع الوظائف"

# انتظار لمدة 10 ثوان للاختبار اليدوي
echo ""
echo "⏱️ انتظار 10 ثوان للاختبار اليدوي..."
for i in {10..1}; do
    echo -ne "\r⏳ $i ثوان متبقية..."
    sleep 1
done
echo ""

# اختبار 8: التقاط لقطة شاشة
echo "📸 اختبار 8: التقاط لقطة شاشة..."
SCREENSHOT_FILE="screenshot-$(date +%Y%m%d_%H%M%S).png"
adb shell screencap -p /sdcard/$SCREENSHOT_FILE
adb pull /sdcard/$SCREENSHOT_FILE .
adb shell rm /sdcard/$SCREENSHOT_FILE

if [ -f "$SCREENSHOT_FILE" ]; then
    echo "✅ تم التقاط لقطة الشاشة: $SCREENSHOT_FILE"
    echo "✅ اختبار لقطة الشاشة: نجح" >> $TEST_RESULTS
else
    echo "❌ فشل في التقاط لقطة الشاشة"
    echo "❌ اختبار لقطة الشاشة: فشل" >> $TEST_RESULTS
fi

# إنهاء التطبيق
echo "🔚 إنهاء التطبيق..."
adb shell am force-stop $APP_PACKAGE

# إنشاء ملخص النتائج
echo ""
echo "📊 ملخص نتائج الاختبار:"
echo "=========================="

PASSED_TESTS=$(grep "✅.*: نجح" $TEST_RESULTS | wc -l)
FAILED_TESTS=$(grep "❌.*: فشل" $TEST_RESULTS | wc -l)
WARNING_TESTS=$(grep "⚠️.*: تحذير" $TEST_RESULTS | wc -l)

echo "✅ الاختبارات الناجحة: $PASSED_TESTS"
echo "❌ الاختبارات الفاشلة: $FAILED_TESTS"
echo "⚠️ التحذيرات: $WARNING_TESTS"

# إضافة الملخص لملف النتائج
echo "" >> $TEST_RESULTS
echo "📊 ملخص النتائج:" >> $TEST_RESULTS
echo "=================" >> $TEST_RESULTS
echo "✅ الاختبارات الناجحة: $PASSED_TESTS" >> $TEST_RESULTS
echo "❌ الاختبارات الفاشلة: $FAILED_TESTS" >> $TEST_RESULTS
echo "⚠️ التحذيرات: $WARNING_TESTS" >> $TEST_RESULTS

# تحديد النتيجة العامة
if [ $FAILED_TESTS -eq 0 ]; then
    if [ $WARNING_TESTS -eq 0 ]; then
        OVERALL_RESULT="ممتاز ✅"
    else
        OVERALL_RESULT="جيد مع تحذيرات ⚠️"
    fi
else
    OVERALL_RESULT="يحتاج إصلاحات ❌"
fi

echo ""
echo "🎯 النتيجة العامة: $OVERALL_RESULT"
echo "📄 تقرير مفصل: $TEST_RESULTS"

echo "" >> $TEST_RESULTS
echo "🎯 النتيجة العامة: $OVERALL_RESULT" >> $TEST_RESULTS

# نصائح للتحسين
echo ""
echo "💡 نصائح للتحسين:"
if [ $FAILED_TESTS -gt 0 ]; then
    echo "   - راجع الأخطاء في ملف $TEST_RESULTS"
    echo "   - أصلح المشاكل وأعد الاختبار"
fi
if [ $WARNING_TESTS -gt 0 ]; then
    echo "   - راجع التحذيرات وحسّن الأداء"
fi
echo "   - اختبر على أجهزة مختلفة"
echo "   - اختبر جميع الوظائف يدوياً"

echo ""
echo "🎉 انتهى الاختبار!"
echo "📱 التطبيق جاهز للمرحلة التالية: إنشاء الأصول البصرية"
