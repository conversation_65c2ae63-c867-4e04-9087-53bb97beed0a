# سكريبت بناء سريع لتطبيق حاسبة الأبراج
# المطور: محمد طعم شريف الخفاجي

Write-Host "🚀 بناء سريع لتطبيق حاسبة الأبراج" -ForegroundColor Cyan
Write-Host "=================================="

# التحقق من Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت" -ForegroundColor Red
    exit 1
}

# تثبيت التبعيات
Write-Host "📦 تثبيت التبعيات..." -ForegroundColor Yellow
npm install

# التحقق من Cordova
try {
    cordova --version | Out-Null
    Write-Host "✅ Cordova متوفر" -ForegroundColor Green
} catch {
    Write-Host "📦 تثبيت Cordova..." -ForegroundColor Yellow
    npm install -g cordova
}

# إضافة منصة أندرويد
Write-Host "🤖 إضافة منصة أندرويد..." -ForegroundColor Yellow
cordova platform add android 2>$null

# إضافة الإضافات الأساسية
Write-Host "🔌 إضافة الإضافات..." -ForegroundColor Yellow
cordova plugin add cordova-plugin-whitelist 2>$null
cordova plugin add cordova-plugin-device 2>$null

# بناء التطبيق
Write-Host "🏗️ بناء التطبيق..." -ForegroundColor Yellow
cordova build android

# التحقق من النتيجة
$apkPath = "platforms\android\app\build\outputs\apk\debug\app-debug.apk"
if (Test-Path $apkPath) {
    Write-Host "✅ تم بناء التطبيق بنجاح!" -ForegroundColor Green
    
    # نسخ الملف
    Copy-Item $apkPath "zodiac-calculator-debug.apk" -Force
    
    $fileSize = [math]::Round((Get-Item "zodiac-calculator-debug.apk").Length / 1MB, 2)
    Write-Host "📱 ملف APK: zodiac-calculator-debug.apk ($fileSize MB)" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "🎉 التطبيق جاهز للتثبيت!" -ForegroundColor Green
    Write-Host "📱 لتثبيت: adb install zodiac-calculator-debug.apk" -ForegroundColor White
} else {
    Write-Host "❌ فشل في بناء التطبيق" -ForegroundColor Red
}
