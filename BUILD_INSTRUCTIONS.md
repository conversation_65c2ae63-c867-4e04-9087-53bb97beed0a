# 📱 تعليمات بناء تطبيق حاسبة الأبراج لأندرويد

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية تحويل تطبيق حاسبة الأبراج الويب إلى تطبيق أندرويد APK باستخدام Apache Cordova.

## 📋 المتطلبات الأساسية

### 1. Node.js و npm
```bash
# تحميل وتثبيت Node.js من الموقع الرسمي
https://nodejs.org/

# التحقق من التثبيت
node --version
npm --version
```

### 2. Apache Cordova
```bash
# تثبيت Cordova عالمياً
npm install -g cordova

# التحقق من التثبيت
cordova --version
```

### 3. Java Development Kit (JDK)
```bash
# تحميل وتثبيت JDK 8 أو أحدث
https://www.oracle.com/java/technologies/javase-downloads.html

# التحقق من التثبيت
java -version
javac -version
```

### 4. Android SDK
```bash
# تحميل Android Studio أو SDK Tools
https://developer.android.com/studio

# تعيين متغير البيئة
export ANDROID_HOME=/path/to/android-sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### 5. Gradle
```bash
# تحميل وتثبيت Gradle
https://gradle.org/install/

# التحقق من التثبيت
gradle --version
```

## 🚀 خطوات البناء

### الطريقة الأولى: استخدام السكريبت التلقائي

```bash
# منح صلاحيات التنفيذ
chmod +x build.sh

# تشغيل سكريبت البناء
./build.sh
```

### الطريقة الثانية: البناء اليدوي

```bash
# 1. تثبيت التبعيات
npm install

# 2. إضافة منصة أندرويد
cordova platform add android

# 3. إضافة الإضافات المطلوبة
cordova plugin add cordova-plugin-whitelist
cordova plugin add cordova-plugin-statusbar
cordova plugin add cordova-plugin-device
cordova plugin add cordova-plugin-splashscreen
cordova plugin add cordova-plugin-network-information
cordova plugin add cordova-plugin-vibration
cordova plugin add cordova-plugin-dialogs

# 4. تحضير المشروع
cordova prepare android

# 5. بناء التطبيق
cordova build android
```

## 📦 بناء نسخة الإنتاج

```bash
# منح صلاحيات التنفيذ
chmod +x build-release.sh

# تشغيل سكريبت بناء الإنتاج
./build-release.sh
```

## 📁 هيكل المشروع

```
zodiac-calculator-app/
├── config.xml              # تكوين Cordova
├── package.json            # تبعيات Node.js
├── build.sh                # سكريبت البناء
├── build-release.sh        # سكريبت بناء الإنتاج
├── www/                    # ملفات الويب
│   ├── index.html
│   ├── style.css
│   ├── script.js
│   └── README.md
├── platforms/              # ملفات المنصات
│   └── android/
├── plugins/                # إضافات Cordova
└── res/                    # الموارد (أيقونات، شاشات)
    ├── icon/
    └── screen/
```

## 🔧 التخصيص والتكوين

### تغيير معرف التطبيق
```xml
<!-- في ملف config.xml -->
<widget id="com.yourname.zodiac.calculator" ...>
```

### تغيير اسم التطبيق
```xml
<!-- في ملف config.xml -->
<name>اسم التطبيق الجديد</name>
```

### إضافة أيقونة مخصصة
```bash
# وضع ملفات الأيقونات في مجلد res/icon/android/
# بأحجام مختلفة: 36x36, 48x48, 72x72, 96x96, 144x144, 192x192
```

## 🐛 حل المشاكل الشائعة

### مشكلة: ANDROID_HOME غير محدد
```bash
# إضافة إلى ملف ~/.bashrc أو ~/.zshrc
export ANDROID_HOME=/path/to/android-sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

### مشكلة: Gradle build failed
```bash
# تنظيف المشروع وإعادة البناء
cordova clean android
cordova build android
```

### مشكلة: Plugin not found
```bash
# إعادة إضافة الإضافات
cordova plugin remove [plugin-name]
cordova plugin add [plugin-name]
```

## 📱 اختبار التطبيق

### على المحاكي
```bash
# تشغيل على المحاكي
cordova emulate android
```

### على جهاز حقيقي
```bash
# تفعيل وضع المطور و USB Debugging
# ثم تشغيل:
cordova run android
```

### تثبيت APK يدوياً
```bash
# باستخدام ADB
adb install zodiac-calculator-v1.0.0.apk

# أو نقل الملف للجهاز وتثبيته يدوياً
```

## 🔐 التوقيع والنشر

### إنشاء مفتاح التوقيع
```bash
keytool -genkey -v -keystore zodiac-calculator.keystore \
        -alias zodiac-calculator -keyalg RSA -keysize 2048 \
        -validity 10000
```

### بناء نسخة موقعة
```bash
cordova build android --release
```

## 📊 معلومات التطبيق النهائي

- **الاسم**: حاسبة الأبراج
- **المعرف**: com.mahm.zodiac.calculator
- **الإصدار**: 1.0.0
- **الحد الأدنى لأندرويد**: API 22 (Android 5.1)
- **الهدف**: API 33 (Android 13)
- **الحجم المتوقع**: 5-10 MB
- **الصلاحيات**: الإنترنت، الشبكة، الاهتزاز

## 👨‍💻 المطور

**محمد طعم شريف الخفاجي**
- تطبيق شامل للأبراج الفلكية
- دعم كامل للغة العربية
- تصميم متجاوب وأنيق

## 📞 الدعم

في حالة مواجهة أي مشاكل:
1. تحقق من تثبيت جميع المتطلبات
2. راجع رسائل الخطأ في Terminal
3. تأكد من إعدادات Android SDK
4. جرب تنظيف المشروع وإعادة البناء

---

🎉 **مبروك! تطبيقك جاهز للاستخدام والتوزيع** 🎉
