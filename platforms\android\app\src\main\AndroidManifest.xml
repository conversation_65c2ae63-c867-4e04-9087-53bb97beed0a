<?xml version='1.0' encoding='utf-8'?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    xmlns:tools="http://schemas.android.com/tools"
    package="com.mahm.zodiac.calculator">
    
    <!-- الصلاحيات المطلوبة -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- دعم الشاشات المختلفة -->
    <supports-screens 
        android:anyDensity="true" 
        android:largeScreens="true" 
        android:normalScreens="true" 
        android:resizeable="true" 
        android:smallScreens="true" 
        android:xlargeScreens="true" />
    
    <application 
        android:name="com.mahm.zodiac.calculator.MainApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        tools:targetApi="28">
        
        <!-- النشاط الرئيسي -->
        <activity 
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/AppTheme.NoActionBar"
            android:screenOrientation="portrait"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection"
            android:windowSoftInputMode="adjustResize">
            
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- شاشة البداية -->
        <activity 
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/SplashTheme"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- مزود المحتوى للملفات -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>
</manifest>
