# 🧪 دليل اختبار تطبيق حاسبة الأبراج

## 🎯 نظرة عامة

هذا الدليل يوضح كيفية اختبار تطبيق حاسبة الأبراج بشكل شامل قبل النشر.

## 📋 قائمة الاختبارات الأساسية

### ✅ **1. اختبار التثبيت**

#### على الجهاز الحقيقي:
```bash
# تثبيت التطبيق
adb install zodiac-calculator-debug.apk

# التحقق من التثبيت الناجح
adb shell pm list packages | grep com.mahm.zodiac.calculator
```

#### نقاط الفحص:
- [ ] التطبيق يثبت بدون أخطاء
- [ ] الأيقونة تظهر في قائمة التطبيقات
- [ ] اسم التطبيق يظهر بالعربية
- [ ] التطبيق يفتح عند الضغط على الأيقونة

### ✅ **2. اختبار الواجهة الأساسية**

#### الشاشة الرئيسية:
- [ ] العنوان يظهر بالعربية: "🌟 حاسبة العمر والبرج الفلكي 🌟"
- [ ] النص التوضيحي يظهر بوضوح
- [ ] حقل إدخال التاريخ يعمل بشكل صحيح
- [ ] زر "✨ احسب الآن ✨" يظهر ويعمل
- [ ] التصميم متجاوب مع حجم الشاشة
- [ ] الألوان والخطوط تظهر بشكل صحيح

#### اختبار RTL (الاتجاه من اليمين لليسار):
- [ ] النصوص العربية تظهر من اليمين لليسار
- [ ] الأزرار والعناصر في المواضع الصحيحة
- [ ] التمرير يعمل في الاتجاه الصحيح

### ✅ **3. اختبار حساب العمر**

#### حالات الاختبار:
```
📅 تاريخ الاختبار 1: 1990-05-15
   النتيجة المتوقعة: العمر الحالي بدقة

📅 تاريخ الاختبار 2: 2000-12-31
   النتيجة المتوقعة: حساب صحيح لنهاية السنة

📅 تاريخ الاختبار 3: 1985-02-29 (سنة كبيسة)
   النتيجة المتوقعة: التعامل الصحيح مع السنة الكبيسة
```

#### نقاط الفحص:
- [ ] حساب السنوات صحيح
- [ ] حساب الأشهر صحيح
- [ ] حساب الأيام صحيح
- [ ] التعامل مع السنوات الكبيسة
- [ ] عرض النتائج بتصميم جميل

### ✅ **4. اختبار التحويل الهجري**

#### نقاط الفحص:
- [ ] التحويل للتاريخ الهجري يعمل
- [ ] التاريخ الهجري يظهر بالعربية
- [ ] دقة التحويل (مقارنة مع مصادر أخرى)
- [ ] التعامل مع التواريخ القديمة والحديثة

### ✅ **5. اختبار تحديد البرج الفلكي**

#### حالات الاختبار:
```
🔥 الحمل: 21 مارس - 19 أبريل
🌍 الثور: 20 أبريل - 20 مايو
💨 الجوزاء: 21 مايو - 20 يونيو
💧 السرطان: 21 يونيو - 22 يوليو
🔥 الأسد: 23 يوليو - 22 أغسطس
🌍 العذراء: 23 أغسطس - 22 سبتمبر
💨 الميزان: 23 سبتمبر - 22 أكتوبر
💧 العقرب: 23 أكتوبر - 21 نوفمبر
🔥 القوس: 22 نوفمبر - 21 ديسمبر
🌍 الجدي: 22 ديسمبر - 19 يناير
💨 الدلو: 20 يناير - 18 فبراير
💧 الحوت: 19 فبراير - 20 مارس
```

#### نقاط الفحص:
- [ ] تحديد البرج الصحيح لكل تاريخ
- [ ] عرض رمز البرج (♈ ♉ ♊ إلخ...)
- [ ] عرض تواريخ البرج
- [ ] عرض وصف البرج
- [ ] عرض العنصر والكوكب الحاكم
- [ ] عرض الحجر الكريم

### ✅ **6. اختبار التوقعات اليومية**

#### نقاط الفحص:
- [ ] التاريخ العربي يظهر بشكل صحيح
- [ ] التوقع يظهر ويتناسب مع البرج
- [ ] زر "🔄 تحديث التوقع" يعمل
- [ ] التوقعات تتغير عند الضغط على التحديث
- [ ] التأثيرات البصرية تعمل عند التحديث
- [ ] التصميم المتدرج يظهر بشكل جميل

### ✅ **7. اختبار المعلومات الإضافية**

#### الصفات الشخصية:
- [ ] الصفات الإيجابية تظهر بتصميم أخضر
- [ ] الصفات السلبية تظهر بتصميم برتقالي
- [ ] جميع الصفات تظهر بشكل صحيح

#### المهن المناسبة:
- [ ] المهن تظهر بتصميم متدرج أنيق
- [ ] المهن مناسبة لشخصية البرج
- [ ] التأثيرات التفاعلية تعمل

#### الأرقام المحظوظة:
- [ ] الأرقام تظهر في دوائر ذهبية
- [ ] التأثيرات البصرية تعمل عند التمرير
- [ ] الأرقام صحيحة لكل برج

#### توافق الأبراج:
- [ ] الأبراج المتوافقة تظهر بتصميم وردي
- [ ] رموز القلوب تظهر
- [ ] التوافق منطقي حسب العناصر

### ✅ **8. اختبار الأداء**

#### سرعة التطبيق:
- [ ] التطبيق يفتح خلال 3 ثوان
- [ ] الحسابات تتم فوراً
- [ ] التنقل بين الأقسام سلس
- [ ] لا توجد تأخيرات ملحوظة

#### استهلاك الذاكرة:
```bash
# فحص استهلاك الذاكرة
adb shell dumpsys meminfo com.mahm.zodiac.calculator
```

#### استهلاك البطارية:
- [ ] التطبيق لا يستنزف البطارية
- [ ] لا توجد عمليات في الخلفية غير ضرورية

### ✅ **9. اختبار التوافق**

#### أحجام الشاشات المختلفة:
- [ ] هواتف صغيرة (4-5 بوصة)
- [ ] هواتف متوسطة (5-6 بوصة)
- [ ] هواتف كبيرة (6+ بوصة)
- [ ] أجهزة لوحية (7+ بوصة)

#### إصدارات أندرويد:
- [ ] أندرويد 5.1 (API 22)
- [ ] أندرويد 6.0 (API 23)
- [ ] أندرويد 7.0 (API 24)
- [ ] أندرويد 8.0+ (API 26+)
- [ ] أندرويد 10+ (API 29+)
- [ ] أندرويد 12+ (API 31+)

### ✅ **10. اختبار معالجة الأخطاء**

#### حالات الخطأ:
- [ ] إدخال تاريخ في المستقبل
- [ ] عدم إدخال تاريخ
- [ ] إدخال تاريخ غير صحيح
- [ ] انقطاع الإنترنت
- [ ] ذاكرة منخفضة

#### رسائل الخطأ:
- [ ] رسائل واضحة بالعربية
- [ ] إرشادات للمستخدم
- [ ] عدم توقف التطبيق

## 🔧 أدوات الاختبار

### 1. اختبار على المحاكي:
```bash
# تشغيل المحاكي
emulator -avd Pixel_4_API_30

# تثبيت التطبيق
adb install zodiac-calculator-debug.apk
```

### 2. اختبار على أجهزة حقيقية:
```bash
# عرض الأجهزة المتصلة
adb devices

# تثبيت على جهاز محدد
adb -s [device_id] install zodiac-calculator-debug.apk
```

### 3. فحص الأخطاء:
```bash
# عرض سجل الأخطاء
adb logcat | grep "com.mahm.zodiac.calculator"
```

## 📊 تقرير الاختبار

### قالب التقرير:
```
🧪 تقرير اختبار تطبيق حاسبة الأبراج
==========================================

📅 تاريخ الاختبار: [التاريخ]
👨‍💻 المختبر: [الاسم]
📱 الجهاز: [نوع الجهاز]
🤖 أندرويد: [إصدار النظام]

✅ الاختبارات الناجحة: [العدد]
❌ الاختبارات الفاشلة: [العدد]
⚠️ المشاكل المكتشفة: [العدد]

📋 تفاصيل المشاكل:
1. [وصف المشكلة]
2. [وصف المشكلة]

💡 التوصيات:
1. [توصية]
2. [توصية]

🎯 النتيجة العامة: [ناجح/يحتاج تحسين]
```

---

🎉 **بعد اكتمال الاختبارات بنجاح، سنكون جاهزين للمرحلة التالية!**

هل تريد البدء في اختبار التطبيق الآن، أم تفضل الانتقال مباشرة لإنشاء الأصول البصرية؟
