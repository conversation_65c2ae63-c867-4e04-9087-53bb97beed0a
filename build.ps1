# سكريبت بناء تطبيق حاسبة الأبراج لنظام أندرويد - PowerShell
# المطور: محمد طعم شريف الخفاجي

Write-Host "🌟 بدء بناء تطبيق حاسبة الأبراج 🌟" -ForegroundColor Cyan
Write-Host "========================================"

# التحقق من وجود Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js متوفر: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً" -ForegroundColor Red
    Write-Host "🌐 تحميل من: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# التحقق من وجود Cordova
try {
    $cordovaVersion = cordova --version
    Write-Host "✅ Cordova متوفر: $cordovaVersion" -ForegroundColor Green
} catch {
    Write-Host "📦 تثبيت Cordova..." -ForegroundColor Yellow
    npm install -g cordova
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل في تثبيت Cordova" -ForegroundColor Red
        exit 1
    }
}

# التحقق من وجود Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✅ Java متوفر: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java غير مثبت. يرجى تثبيت Java JDK 8 أو أحدث" -ForegroundColor Red
    Write-Host "🌐 تحميل من: https://www.oracle.com/java/technologies/javase-downloads.html" -ForegroundColor Yellow
    exit 1
}

# التحقق من وجود Android SDK
if (-not $env:ANDROID_HOME) {
    Write-Host "❌ متغير ANDROID_HOME غير محدد. يرجى تثبيت Android SDK" -ForegroundColor Red
    Write-Host "🌐 تحميل Android Studio من: https://developer.android.com/studio" -ForegroundColor Yellow
    Write-Host "💡 أو قم بتعيين ANDROID_HOME يدوياً" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "✅ Android SDK متوفر: $env:ANDROID_HOME" -ForegroundColor Green
}

Write-Host ""
Write-Host "✅ جميع المتطلبات متوفرة" -ForegroundColor Green
Write-Host ""

# تثبيت التبعيات
Write-Host "📦 تثبيت التبعيات..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تثبيت التبعيات" -ForegroundColor Red
    exit 1
}

# إضافة منصة أندرويد
Write-Host "🤖 إضافة منصة أندرويد..." -ForegroundColor Yellow
cordova platform add android
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ منصة أندرويد موجودة مسبقاً أو تمت إضافتها" -ForegroundColor Yellow
}

# إضافة الإضافات المطلوبة
Write-Host "🔌 إضافة الإضافات..." -ForegroundColor Yellow

$plugins = @(
    "cordova-plugin-whitelist",
    "cordova-plugin-statusbar", 
    "cordova-plugin-device",
    "cordova-plugin-splashscreen",
    "cordova-plugin-network-information",
    "cordova-plugin-vibration",
    "cordova-plugin-dialogs"
)

foreach ($plugin in $plugins) {
    Write-Host "   📌 إضافة $plugin..." -ForegroundColor Cyan
    cordova plugin add $plugin
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ تم إضافة $plugin" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️ $plugin موجود مسبقاً أو تمت إضافته" -ForegroundColor Yellow
    }
}

# التحضير للبناء
Write-Host "🔧 تحضير المشروع..." -ForegroundColor Yellow
cordova prepare android
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تحضير المشروع" -ForegroundColor Red
    exit 1
}

# بناء التطبيق للتطوير
Write-Host "🏗️ بناء التطبيق (وضع التطوير)..." -ForegroundColor Yellow
cordova build android

# التحقق من نجاح البناء
if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "✅ تم بناء التطبيق بنجاح!" -ForegroundColor Green
    Write-Host "📱 ملف APK متوفر في: platforms\android\app\build\outputs\apk\debug\" -ForegroundColor Cyan
    Write-Host ""
    
    # عرض معلومات الملف
    $apkFile = "platforms\android\app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkFile) {
        $fileInfo = Get-Item $apkFile
        $fileSize = [math]::Round($fileInfo.Length / 1MB, 2)
        
        Write-Host "📊 معلومات الملف:" -ForegroundColor Cyan
        Write-Host "   📁 المسار: $apkFile" -ForegroundColor White
        Write-Host "   📏 الحجم: $fileSize MB" -ForegroundColor White
        Write-Host "   📅 التاريخ: $($fileInfo.LastWriteTime)" -ForegroundColor White
        
        # نسخ الملف إلى المجلد الرئيسي مع اسم أفضل
        $outputFile = "zodiac-calculator-debug.apk"
        Copy-Item $apkFile $outputFile -Force
        Write-Host "   📋 نسخة سهلة: $outputFile" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "🚀 لتشغيل التطبيق على جهاز متصل:" -ForegroundColor Yellow
    Write-Host "   cordova run android" -ForegroundColor White
    Write-Host ""
    Write-Host "📦 لبناء نسخة الإنتاج:" -ForegroundColor Yellow
    Write-Host "   .\build-release.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "📱 لتثبيت التطبيق:" -ForegroundColor Yellow
    Write-Host "   adb install $outputFile" -ForegroundColor White
    
} else {
    Write-Host ""
    Write-Host "❌ فشل في بناء التطبيق" -ForegroundColor Red
    Write-Host "🔍 تحقق من الأخطاء أعلاه وحاول مرة أخرى" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "🎉 انتهى بناء التطبيق بنجاح!" -ForegroundColor Green
Write-Host "👨‍💻 المطور: محمد طعم شريف الخفاجي" -ForegroundColor Cyan

# عرض الخطوات التالية
Write-Host ""
Write-Host "🎯 الخطوات التالية:" -ForegroundColor Yellow
Write-Host "   1. 🧪 اختبار التطبيق: .\test-app.ps1" -ForegroundColor White
Write-Host "   2. 🎨 إنشاء الأصول البصرية: .\create-assets.ps1" -ForegroundColor White
Write-Host "   3. 🏪 النشر على Google Play Store" -ForegroundColor White

Write-Host ""
Write-Host "📁 ملف APK جاهز: zodiac-calculator-debug.apk" -ForegroundColor Green
Write-Host "📏 الحجم: $(if (Test-Path 'zodiac-calculator-debug.apk') { [math]::Round((Get-Item 'zodiac-calculator-debug.apk').Length / 1MB, 2) } else { 'غير معروف' }) MB" -ForegroundColor Green
