<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- التصميم الأساسي للتطبيق -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">#667eea</item>
        <item name="colorPrimaryDark">#5a67d8</item>
        <item name="colorAccent">#764ba2</item>
        <item name="android:textColorPrimary">#333333</item>
        <item name="android:textColorSecondary">#666666</item>
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:fontFamily">@font/cairo_regular</item>
        <item name="android:layoutDirection">rtl</item>
        <item name="android:textDirection">rtl</item>
    </style>

    <!-- تصميم بدون شريط الأدوات -->
    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">#667eea</item>
        <item name="android:navigationBarColor">#667eea</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <!-- تصميم شاشة البداية -->
    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">#667eea</item>
        <item name="android:navigationBarColor">#667eea</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- تصميم الأزرار -->
    <style name="ButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_gradient</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/cairo_bold</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@animator/button_elevation</item>
    </style>

    <!-- تصميم النصوص -->
    <style name="HeaderTextStyle">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:fontFamily">@font/cairo_bold</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_margin">16dp</item>
    </style>

    <style name="BodyTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#666666</item>
        <item name="android:fontFamily">@font/cairo_regular</item>
        <item name="android:lineSpacingExtra">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- تصميم البطاقات -->
    <style name="CardStyle">
        <item name="android:background">@drawable/card_background</item>
        <item name="android:elevation">8dp</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:padding">20dp</item>
    </style>

    <!-- تصميم حقول الإدخال -->
    <style name="InputStyle" parent="Widget.AppCompat.EditText">
        <item name="android:background">@drawable/input_background</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/cairo_regular</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:textDirection">rtl</item>
        <item name="android:gravity">center</item>
    </style>
</resources>
