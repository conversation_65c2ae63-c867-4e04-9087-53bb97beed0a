#!/bin/bash

# سكريبت بناء نسخة الإنتاج لتطبيق حاسبة الأبراج
# المطور: محمد طعم شريف الخفاجي

echo "🚀 بناء نسخة الإنتاج لتطبيق حاسبة الأبراج 🚀"
echo "=================================================="

# التحقق من وجود ملف المفاتيح
KEYSTORE_FILE="zodiac-calculator.keystore"
if [ ! -f "$KEYSTORE_FILE" ]; then
    echo "🔐 إنشاء ملف المفاتيح..."
    keytool -genkey -v -keystore $KEYSTORE_FILE -alias zodiac-calculator \
            -keyalg RSA -keysize 2048 -validity 10000 \
            -dname "CN=محمد طعم شريف الخفاجي, OU=Mobile Development, O=MAHM, L=Baghdad, ST=Baghdad, C=IQ" \
            -storepass zodiac123 -keypass zodiac123
    
    if [ $? -eq 0 ]; then
        echo "✅ تم إنشاء ملف المفاتيح بنجاح"
    else
        echo "❌ فشل في إنشاء ملف المفاتيح"
        exit 1
    fi
fi

# إنشاء ملف build.json للتوقيع
echo "📝 إنشاء ملف التكوين للتوقيع..."
cat > build.json << EOF
{
    "android": {
        "release": {
            "keystore": "$KEYSTORE_FILE",
            "storePassword": "zodiac123",
            "alias": "zodiac-calculator",
            "password": "zodiac123",
            "keystoreType": "jks"
        }
    }
}
EOF

# تنظيف البناء السابق
echo "🧹 تنظيف البناء السابق..."
cordova clean android

# التحضير للبناء
echo "🔧 تحضير المشروع..."
cordova prepare android

# بناء نسخة الإنتاج
echo "🏗️ بناء نسخة الإنتاج..."
cordova build android --release

# التحقق من نجاح البناء
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ تم بناء نسخة الإنتاج بنجاح!"
    
    # البحث عن ملف APK
    RELEASE_APK="platforms/android/app/build/outputs/apk/release/app-release.apk"
    UNSIGNED_APK="platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk"
    
    if [ -f "$RELEASE_APK" ]; then
        echo "📱 ملف APK الموقع متوفر في: $RELEASE_APK"
        
        # عرض معلومات الملف
        echo ""
        echo "📊 معلومات ملف الإنتاج:"
        echo "   📁 المسار: $RELEASE_APK"
        echo "   📏 الحجم: $(du -h "$RELEASE_APK" | cut -f1)"
        echo "   📅 التاريخ: $(date -r "$RELEASE_APK" '+%Y-%m-%d %H:%M:%S')"
        
        # نسخ الملف مع اسم أفضل
        FINAL_APK="zodiac-calculator-v1.0.0.apk"
        cp "$RELEASE_APK" "$FINAL_APK"
        echo "   📦 نسخة نهائية: $FINAL_APK"
        
    elif [ -f "$UNSIGNED_APK" ]; then
        echo "⚠️ ملف APK غير موقع متوفر في: $UNSIGNED_APK"
        echo "🔐 يرجى توقيع الملف يدوياً باستخدام jarsigner"
        
    else
        echo "❌ لم يتم العثور على ملف APK"
        exit 1
    fi
    
    echo ""
    echo "📋 خطوات التثبيت:"
    echo "   1. انقل ملف APK إلى جهاز أندرويد"
    echo "   2. فعّل 'مصادر غير معروفة' في إعدادات الأمان"
    echo "   3. اضغط على ملف APK لتثبيت التطبيق"
    
    echo ""
    echo "🔍 لاختبار التطبيق:"
    echo "   adb install $FINAL_APK"
    
else
    echo ""
    echo "❌ فشل في بناء نسخة الإنتاج"
    echo "🔍 تحقق من الأخطاء أعلاه وحاول مرة أخرى"
    exit 1
fi

echo ""
echo "🎉 انتهى بناء نسخة الإنتاج بنجاح!"
echo "👨‍💻 المطور: محمد طعم شريف الخفاجي"
echo "📱 التطبيق جاهز للنشر والتوزيع"
