<?xml version='1.0' encoding='utf-8'?>
<widget id="com.mahm.zodiac.calculator" version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
    <name>حاسبة الأبراج</name>
    <description>
        تطبيق شامل لحساب العمر والبرج الفلكي مع التوقعات اليومية والتحويل للتاريخ الهجري
    </description>
    <author email="<EMAIL>" href="https://github.com/mahm">
        محمد طعم شريف الخفاجي
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    
    <!-- Android Configuration -->
    <platform name="android">
        <allow-intent href="market:*" />
        <icon density="ldpi" src="res/icon/android/ldpi.png" />
        <icon density="mdpi" src="res/icon/android/mdpi.png" />
        <icon density="hdpi" src="res/icon/android/hdpi.png" />
        <icon density="xhdpi" src="res/icon/android/xhdpi.png" />
        <icon density="xxhdpi" src="res/icon/android/xxhdpi.png" />
        <icon density="xxxhdpi" src="res/icon/android/xxxhdpi.png" />
        <splash density="land-ldpi" src="res/screen/android/splash-land-ldpi.png" />
        <splash density="land-mdpi" src="res/screen/android/splash-land-mdpi.png" />
        <splash density="land-hdpi" src="res/screen/android/splash-land-hdpi.png" />
        <splash density="land-xhdpi" src="res/screen/android/splash-land-xhdpi.png" />
        <splash density="land-xxhdpi" src="res/screen/android/splash-land-xxhdpi.png" />
        <splash density="land-xxxhdpi" src="res/screen/android/splash-land-xxxhdpi.png" />
        <splash density="port-ldpi" src="res/screen/android/splash-port-ldpi.png" />
        <splash density="port-mdpi" src="res/screen/android/splash-port-mdpi.png" />
        <splash density="port-hdpi" src="res/screen/android/splash-port-hdpi.png" />
        <splash density="port-xhdpi" src="res/screen/android/splash-port-xhdpi.png" />
        <splash density="port-xxhdpi" src="res/screen/android/splash-port-xxhdpi.png" />
        <splash density="port-xxxhdpi" src="res/screen/android/splash-port-xxxhdpi.png" />
        
        <!-- Android Preferences -->
        <preference name="android-minSdkVersion" value="22" />
        <preference name="android-targetSdkVersion" value="33" />
        <preference name="android-compileSdkVersion" value="33" />
        <preference name="Orientation" value="portrait" />
        <preference name="Fullscreen" value="false" />
        <preference name="StatusBarOverlaysWebView" value="false" />
        <preference name="StatusBarBackgroundColor" value="#667eea" />
        <preference name="StatusBarStyle" value="lightcontent" />
    </platform>
    
    <!-- Global Preferences -->
    <preference name="DisallowOverscroll" value="true" />
    <preference name="BackgroundColor" value="0xff667eea" />
    <preference name="HideKeyboardFormAccessoryBar" value="true" />
    <preference name="Orientation" value="portrait" />
    <preference name="SplashScreen" value="screen" />
    <preference name="SplashScreenDelay" value="3000" />
    <preference name="AutoHideSplashScreen" value="true" />
    <preference name="SplashShowOnlyFirstTime" value="false" />
    <preference name="FadeSplashScreen" value="true" />
    <preference name="FadeSplashScreenDuration" value="750" />
    <preference name="ShowSplashScreenSpinner" value="false" />
    
    <!-- Plugins -->
    <plugin name="cordova-plugin-whitelist" spec="^1.3.4" />
    <plugin name="cordova-plugin-statusbar" spec="^2.4.2" />
    <plugin name="cordova-plugin-device" spec="^2.0.3" />
    <plugin name="cordova-plugin-splashscreen" spec="^5.0.3" />
    <plugin name="cordova-plugin-network-information" spec="^2.0.2" />
    <plugin name="cordova-plugin-vibration" spec="^3.1.1" />
    <plugin name="cordova-plugin-dialogs" spec="^2.0.2" />
    
    <!-- Content Security Policy -->
    <allow-navigation href="*" />
    <allow-navigation href="https://*" />
    <allow-navigation href="data:*" />
</widget>
