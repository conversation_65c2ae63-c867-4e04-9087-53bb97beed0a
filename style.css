/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* رأس الصفحة */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.9;
}

/* قسم الإدخال */
.input-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.input-group {
    margin-bottom: 25px;
}

.input-group label {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.input-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    direction: ltr;
    text-align: center;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.calculate-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.calculate-btn:active {
    transform: translateY(0);
}

/* قسم النتائج */
.results-section {
    flex: 1;
}

.hidden {
    display: none;
}

.result-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
}

.result-card h2 {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #333;
}

.result-item {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
    border-right: 5px solid #667eea;
}

.result-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.age-display {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
}

.age-unit {
    background: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    min-width: 100px;
}

.age-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.age-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

.hijri-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.zodiac-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.zodiac-info {
    text-align: center;
    margin-bottom: 15px;
}

.zodiac-name {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.zodiac-dates {
    display: block;
    font-size: 1rem;
    color: #666;
}

.zodiac-color-display {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    font-weight: 600;
}

.favorite-color {
    font-weight: 700;
    padding: 5px 15px;
    border-radius: 20px;
    color: white;
    margin-right: 10px;
}

/* ذيل الصفحة */
footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    font-weight: 300;
    opacity: 0.8;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-section,
    .result-card {
        padding: 20px;
    }
    
    .age-display {
        flex-direction: column;
        align-items: center;
    }
    
    .age-unit {
        width: 100%;
        max-width: 200px;
    }
}
