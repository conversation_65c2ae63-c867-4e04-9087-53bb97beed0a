/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* رأس الصفحة */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    font-weight: 300;
    opacity: 0.9;
}

/* قسم الإدخال */
.input-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.input-group {
    margin-bottom: 25px;
}

.input-group label {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.input-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    direction: ltr;
    text-align: center;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.calculate-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.calculate-btn:active {
    transform: translateY(0);
}

/* قسم النتائج */
.results-section {
    flex: 1;
}

.hidden {
    display: none;
}

.result-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
}

.result-card h2 {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #333;
}

.result-item {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
    border-right: 5px solid #667eea;
}

.result-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.age-display {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
}

.age-unit {
    background: white;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    min-width: 100px;
}

.age-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.age-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

.hijri-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.zodiac-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.zodiac-info {
    text-align: center;
    margin-bottom: 15px;
}

.zodiac-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 10px;
}

.zodiac-symbol {
    font-size: 2rem;
    color: #667eea;
}

.zodiac-name {
    font-size: 1.5rem;
    font-weight: 700;
}

.zodiac-dates {
    display: block;
    font-size: 1rem;
    color: #666;
    margin-bottom: 15px;
}

.zodiac-description {
    font-size: 0.95rem;
    color: #555;
    line-height: 1.6;
    padding: 10px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    margin-top: 10px;
}

.zodiac-color-display {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    font-weight: 600;
}

.favorite-color {
    font-weight: 700;
    padding: 5px 15px;
    border-radius: 20px;
    color: white;
    margin-right: 10px;
}

/* قسم الصفات */
.traits-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.traits-section {
    margin-bottom: 20px;
}

.traits-section:last-child {
    margin-bottom: 0;
}

.traits-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-align: center;
    padding: 8px;
    border-radius: 8px;
}

.positive-title {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
    color: white;
}

.negative-title {
    background: linear-gradient(45deg, #FF9800, #FF5722);
    color: white;
}

.traits-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.trait-tag {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
    cursor: default;
}

.positive-traits .trait-tag {
    background: linear-gradient(45deg, #E8F5E8, #C8E6C9);
    color: #2E7D32;
    border: 1px solid #4CAF50;
}

.negative-traits .trait-tag {
    background: linear-gradient(45deg, #FFF3E0, #FFE0B2);
    color: #E65100;
    border: 1px solid #FF9800;
}

.trait-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* قسم معلومات البرج */
.zodiac-info-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border-right: 4px solid #667eea;
}

.info-label {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.info-value {
    font-weight: 500;
    color: #667eea;
    font-size: 1rem;
}

/* قسم المهن المناسبة */
.careers-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.career-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.career-tag {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: default;
}

.career-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* قسم الأرقام المحظوظة */
.lucky-numbers-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.numbers-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.number-tag {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 700;
    transition: all 0.3s ease;
    cursor: default;
    box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
}

.number-tag:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.5);
}

/* قسم التوافق */
.compatibility-display {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.compatible-signs {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.compatibility-tag {
    background: linear-gradient(45deg, #FF69B4, #FF1493);
    color: white;
    padding: 10px 18px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: default;
    position: relative;
}

.compatibility-tag:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
}

.compatibility-tag::before {
    content: '💕';
    margin-left: 8px;
}

/* قسم نصيحة اليوم */
.daily-tip-display {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
}

.daily-tip-text {
    color: white;
    font-size: 1.1rem;
    line-height: 1.8;
    text-align: center;
    font-weight: 500;
    position: relative;
}

.daily-tip-text::before {
    content: '💡';
    font-size: 1.5rem;
    display: block;
    margin-bottom: 15px;
}

.daily-tip-text::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 2px;
}

/* قسم التوقعات اليومية */
.daily-prediction-display {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
    position: relative;
    overflow: hidden;
}

.daily-prediction-display::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.prediction-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 15px;
}

.prediction-date {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.prediction-source {
    font-size: 0.9rem;
    opacity: 0.8;
    font-style: italic;
}

.daily-prediction-text {
    font-size: 1.2rem;
    line-height: 1.8;
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border-right: 4px solid #FFD700;
    position: relative;
}

.daily-prediction-text::before {
    content: '🌟';
    position: absolute;
    top: -10px;
    right: 15px;
    font-size: 1.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 5px 10px;
    border-radius: 50%;
}

.prediction-footer {
    text-align: center;
    margin-top: 20px;
}

.refresh-prediction-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.refresh-prediction-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
    background: linear-gradient(45deg, #FFA500, #FFD700);
}

.refresh-prediction-btn:active {
    transform: translateY(0);
}

/* ذيل الصفحة */
footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    font-weight: 300;
    opacity: 0.8;
}

footer p {
    margin: 5px 0;
}

footer p:last-child {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-section,
    .result-card {
        padding: 20px;
    }
    
    .age-display {
        flex-direction: column;
        align-items: center;
    }
    
    .age-unit {
        width: 100%;
        max-width: 200px;
    }

    .traits-list {
        justify-content: center;
    }

    .trait-tag {
        font-size: 0.8rem;
        padding: 5px 10px;
    }

    .zodiac-header {
        flex-direction: column;
        gap: 5px;
    }

    .zodiac-symbol {
        font-size: 1.5rem;
    }

    .info-item {
        flex-direction: column;
        text-align: center;
        gap: 5px;
    }

    .zodiac-description {
        font-size: 0.9rem;
        padding: 8px;
    }

    .career-tag {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .number-tag {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .compatibility-tag {
        font-size: 0.9rem;
        padding: 8px 14px;
    }

    .daily-tip-text {
        font-size: 1rem;
        padding: 15px;
    }

    .numbers-list {
        gap: 10px;
    }

    .career-list,
    .compatible-signs {
        gap: 8px;
    }

    .daily-prediction-display {
        padding: 20px;
    }

    .prediction-date {
        font-size: 1.1rem;
    }

    .daily-prediction-text {
        font-size: 1rem;
        padding: 15px;
    }

    .refresh-prediction-btn {
        font-size: 0.9rem;
        padding: 10px 20px;
    }

    .prediction-source {
        font-size: 0.8rem;
    }
}
