// بيانات الأبراج الفلكية مع الألوان المفضلة
const zodiacSigns = {
    'الحمل': {
        dates: '21 مارس - 19 أبريل',
        color: '#DC143C',
        colorName: 'أحمر'
    },
    'الثور': {
        dates: '20 أبريل - 20 مايو',
        color: '#228B22',
        colorName: 'أخضر'
    },
    'الجوزاء': {
        dates: '21 مايو - 20 يونيو',
        color: '#FFD700',
        colorName: 'أصفر'
    },
    'السرطان': {
        dates: '21 يونيو - 22 يوليو',
        color: '#C0C0C0',
        colorName: 'فضي'
    },
    'الأسد': {
        dates: '23 يوليو - 22 أغسطس',
        color: '#FF8C00',
        colorName: 'برتقالي'
    },
    'العذراء': {
        dates: '23 أغسطس - 22 سبتمبر',
        color: '#4169E1',
        colorName: 'أزرق'
    },
    'الميزان': {
        dates: '23 سبتمبر - 22 أكتوبر',
        color: '#FFB6C1',
        colorName: 'وردي'
    },
    'العقرب': {
        dates: '23 أكتوبر - 21 نوفمبر',
        color: '#8B0000',
        colorName: 'أحمر قاتم'
    },
    'القوس': {
        dates: '22 نوفمبر - 21 ديسمبر',
        color: '#9370DB',
        colorName: 'بنفسجي'
    },
    'الجدي': {
        dates: '22 ديسمبر - 19 يناير',
        color: '#8B4513',
        colorName: 'بني'
    },
    'الدلو': {
        dates: '20 يناير - 18 فبراير',
        color: '#00BFFF',
        colorName: 'أزرق فاتح'
    },
    'الحوت': {
        dates: '19 فبراير - 20 مارس',
        color: '#008B8B',
        colorName: 'أخضر مائي'
    }
};

// دالة تحديد البرج الفلكي
function getZodiacSign(month, day) {
    if ((month == 3 && day >= 21) || (month == 4 && day <= 19)) return 'الحمل';
    if ((month == 4 && day >= 20) || (month == 5 && day <= 20)) return 'الثور';
    if ((month == 5 && day >= 21) || (month == 6 && day <= 20)) return 'الجوزاء';
    if ((month == 6 && day >= 21) || (month == 7 && day <= 22)) return 'السرطان';
    if ((month == 7 && day >= 23) || (month == 8 && day <= 22)) return 'الأسد';
    if ((month == 8 && day >= 23) || (month == 9 && day <= 22)) return 'العذراء';
    if ((month == 9 && day >= 23) || (month == 10 && day <= 22)) return 'الميزان';
    if ((month == 10 && day >= 23) || (month == 11 && day <= 21)) return 'العقرب';
    if ((month == 11 && day >= 22) || (month == 12 && day <= 21)) return 'القوس';
    if ((month == 12 && day >= 22) || (month == 1 && day <= 19)) return 'الجدي';
    if ((month == 1 && day >= 20) || (month == 2 && day <= 18)) return 'الدلو';
    if ((month == 2 && day >= 19) || (month == 3 && day <= 20)) return 'الحوت';
}

// دالة حساب العمر
function calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    
    let years = today.getFullYear() - birth.getFullYear();
    let months = today.getMonth() - birth.getMonth();
    let days = today.getDate() - birth.getDate();
    
    if (days < 0) {
        months--;
        const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        days += lastMonth.getDate();
    }
    
    if (months < 0) {
        years--;
        months += 12;
    }
    
    return { years, months, days };
}

// دالة تحويل التاريخ للهجري
function convertToHijri(gregorianDate) {
    try {
        const hijriDate = moment(gregorianDate).format('iYYYY/iM/iD');
        const hijriDateFormatted = moment(gregorianDate).format('iD iMMMM iYYYY');
        return hijriDateFormatted;
    } catch (error) {
        console.error('خطأ في تحويل التاريخ الهجري:', error);
        return 'غير متاح';
    }
}

// دالة عرض النتائج
function displayResults(birthDate) {
    const age = calculateAge(birthDate);
    const birth = new Date(birthDate);
    const zodiac = getZodiacSign(birth.getMonth() + 1, birth.getDate());
    const hijriDate = convertToHijri(birthDate);
    
    // عرض العمر
    const ageDisplay = document.getElementById('age-result');
    ageDisplay.innerHTML = `
        <div class="age-unit">
            <span class="age-number">${age.years}</span>
            <span class="age-label">سنة</span>
        </div>
        <div class="age-unit">
            <span class="age-number">${age.months}</span>
            <span class="age-label">شهر</span>
        </div>
        <div class="age-unit">
            <span class="age-number">${age.days}</span>
            <span class="age-label">يوم</span>
        </div>
    `;
    
    // عرض التاريخ الهجري
    document.getElementById('hijri-result').textContent = hijriDate;
    
    // عرض البرج الفلكي
    const zodiacInfo = zodiacSigns[zodiac];
    document.getElementById('zodiac-name').textContent = zodiac;
    document.getElementById('zodiac-dates').textContent = zodiacInfo.dates;
    document.getElementById('favorite-color').textContent = zodiacInfo.colorName;
    document.getElementById('favorite-color').style.backgroundColor = zodiacInfo.color;
    
    // تغيير لون خلفية قسم البرج
    const zodiacColorDisplay = document.querySelector('.zodiac-color-display');
    zodiacColorDisplay.style.backgroundColor = zodiacInfo.color + '20'; // شفافية 20%
    
    // تغيير لون خلفية كامل النتائج
    const resultCard = document.querySelector('.result-card');
    resultCard.style.background = `linear-gradient(135deg, rgba(255,255,255,0.95) 0%, ${zodiacInfo.color}20 100%)`;
    
    // إظهار النتائج
    document.getElementById('results').classList.remove('hidden');
    
    // التمرير للنتائج
    document.getElementById('results').scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
}

// إعداد الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    const birthdateInput = document.getElementById('birthdate');
    
    calculateBtn.addEventListener('click', function() {
        const birthDate = birthdateInput.value;
        
        if (!birthDate) {
            alert('⚠️ يرجى إدخال تاريخ الميلاد أولاً');
            return;
        }
        
        const selectedDate = new Date(birthDate);
        const today = new Date();
        
        if (selectedDate > today) {
            alert('⚠️ تاريخ الميلاد لا يمكن أن يكون في المستقبل');
            return;
        }
        
        displayResults(birthDate);
    });
    
    // إضافة حدث الضغط على Enter
    birthdateInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            calculateBtn.click();
        }
    });
});

// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي
    const container = document.querySelector('.container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        container.style.transition = 'all 0.8s ease';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);
});
