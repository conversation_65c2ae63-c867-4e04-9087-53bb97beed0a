// نظام التوقعات اليومية المتقدم
const dailyPredictions = {
    'الحمل': [
        'يوم مليء بالطاقة والحيوية. استغل هذه الطاقة في بدء مشاريع جديدة.',
        'قد تواجه تحديات في العمل، لكن ثقتك بنفسك ستساعدك على التغلب عليها.',
        'يوم مناسب للقرارات المهمة. اعتمد على حدسك الأول.',
        'العلاقات العاطفية في تحسن. تواصل مع الأشخاص المهمين في حياتك.',
        'احذر من التسرع في اتخاذ القرارات المالية اليوم.',
        'يوم رائع للأنشطة الرياضية والحركة. جسمك يحتاج للنشاط.',
        'قد تتلقى أخباراً سارة من صديق قديم أو زميل عمل.'
    ],
    'الثور': [
        'يوم هادئ ومستقر. استمتع بالأشياء البسيطة في الحياة.',
        'الصبر مفتاح النجاح اليوم. لا تتعجل النتائج.',
        'فرصة جيدة لتحسين وضعك المالي أو الاستثمار.',
        'العلاقات الأسرية تحتاج لاهتمامك اليوم.',
        'تجنب الجدالات غير المجدية. السلام أهم من كونك محقاً.',
        'يوم مناسب للأعمال الفنية والإبداعية.',
        'قد تجد حلولاً لمشاكل قديمة كانت تؤرقك.'
    ],
    'الجوزاء': [
        'يوم مليء بالتواصل والأنشطة الاجتماعية.',
        'فضولك الطبيعي سيقودك لاكتشافات مثيرة اليوم.',
        'احذر من تشتت الانتباه. ركز على مهمة واحدة في كل مرة.',
        'فرصة للتعلم أو اكتساب مهارة جديدة.',
        'العلاقات مع الأصدقاء في أفضل حالاتها.',
        'قد تتلقى دعوة لحدث اجتماعي مهم.',
        'يوم مناسب للسفر أو التخطيط لرحلة قريبة.'
    ],
    'السرطان': [
        'مشاعرك حساسة اليوم. اعتن بنفسك عاطفياً.',
        'العائلة والبيت يحتلان الأولوية في يومك.',
        'حدسك قوي جداً اليوم. ثق بمشاعرك الأولى.',
        'قد تشعر بالحنين لذكريات جميلة من الماضي.',
        'يوم مناسب للطبخ أو تحضير وجبة خاصة للأحباب.',
        'تجنب الأشخاص السلبيين الذين يستنزفون طاقتك.',
        'العلاقات العاطفية تحتاج لمزيد من الاهتمام والرعاية.'
    ],
    'الأسد': [
        'أنت في دائرة الضوء اليوم. استمتع بالاهتمام الذي تتلقاه.',
        'ثقتك بنفسك عالية. استغل ذلك في تحقيق أهدافك.',
        'يوم رائع للعروض التقديمية أو الظهور العام.',
        'كرمك وسخاؤك سيجلبان لك محبة الآخرين.',
        'احذر من الغرور أو التكبر على الآخرين.',
        'فرصة للإبداع والتعبير عن موهبتك الفنية.',
        'قد تتلقى مجاملة أو تقديراً لعملك الجاد.'
    ],
    'العذراء': [
        'يوم مثالي للتنظيم وترتيب الأمور المعلقة.',
        'دقتك في التفاصيل ستؤدي إلى نتائج ممتازة.',
        'صحتك تحتاج لاهتمام. اعتن بنظامك الغذائي.',
        'فرصة لمساعدة شخص محتاج أو تقديم النصح.',
        'تجنب الانتقاد المفرط للآخرين اليوم.',
        'العمل والمسؤوليات تتطلب تركيزك الكامل.',
        'قد تكتشف خطأ مهم كان مخفياً عن الآخرين.'
    ],
    'الميزان': [
        'البحث عن التوازن والانسجام هو مفتاح يومك.',
        'العلاقات والشراكات في المقدمة اليوم.',
        'قد تحتاج لاتخاذ قرار مهم. خذ وقتك في التفكير.',
        'الجمال والفن يلهمانك اليوم. استمتع بهما.',
        'تجنب الصراعات والجدالات. السلام أولوية.',
        'فرصة لحل خلاف قديم أو إصلاح علاقة متوترة.',
        'حسك الدبلوماسي سيساعدك في موقف حساس.'
    ],
    'العقرب': [
        'يوم مليء بالكثافة العاطفية والعمق.',
        'حدسك قوي جداً. ثق بإحساسك الداخلي.',
        'قد تكشف سراً مهماً أو تصل لحقيقة مخفية.',
        'العلاقات العميقة والصادقة تحتل الأولوية.',
        'تجنب الغيرة أو الشك غير المبرر.',
        'فرصة للتحول والتجديد في جانب من حياتك.',
        'قوتك الداخلية ستساعدك على تجاوز أي تحدي.'
    ],
    'القوس': [
        'يوم مليء بالتفاؤل والمغامرة.',
        'رغبتك في الاستطلاع ستقودك لتجارب جديدة.',
        'فرصة للسفر أو التخطيط لرحلة مثيرة.',
        'التعلم والتوسع في المعرفة يجلبان لك السعادة.',
        'تجنب المبالغة في الوعود أو التوقعات.',
        'حريتك واستقلاليتك مهمتان اليوم.',
        'قد تلتقي بشخص من ثقافة مختلفة يثري تجربتك.'
    ],
    'الجدي': [
        'يوم للعمل الجاد والتركيز على الأهداف طويلة المدى.',
        'صبرك ومثابرتك ستؤتيان ثمارهما قريباً.',
        'فرصة للتقدم في المسار المهني أو الحصول على ترقية.',
        'المسؤوليات كثيرة لكن قدرتك على التحمل عالية.',
        'تجنب التشاؤم أو النظرة السوداوية للأمور.',
        'الانضباط والتنظيم مفتاحا نجاحك اليوم.',
        'قد تتلقى تقديراً من شخص في موقع سلطة.'
    ],
    'الدلو': [
        'يوم للإبداع والتفكير خارج الصندوق.',
        'أفكارك المبتكرة ستجد آذاناً صاغية اليوم.',
        'الأصدقاء والمجتمع يلعبان دوراً مهماً في يومك.',
        'فرصة للمشاركة في قضية إنسانية أو اجتماعية.',
        'تجنب العناد أو رفض آراء الآخرين بلا مبرر.',
        'التكنولوجيا والحداثة تجلبان لك فرصاً جديدة.',
        'استقلاليتك وتفردك يميزانك عن الآخرين اليوم.'
    ],
    'الحوت': [
        'يوم حالم ومليء بالإلهام والإبداع.',
        'حدسك وحساسيتك العالية تقودانك للصواب.',
        'الفن والموسيقى يجلبان لك السلام الداخلي.',
        'تعاطفك مع الآخرين يجعلك محبوباً.',
        'تجنب الهروب من الواقع أو المسؤوليات.',
        'الروحانية والتأمل يساعدانك على الوضوح.',
        'قد تحلم حلماً مهماً يحمل رسالة لك.'
    ]
};

// دالة للحصول على التوقع اليومي
function getDailyPrediction(zodiacSign) {
    const predictions = dailyPredictions[zodiacSign];
    if (!predictions) return 'لا توجد توقعات متاحة حالياً.';

    // استخدام التاريخ الحالي لاختيار توقع ثابت لليوم
    const today = new Date();
    const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 1000 / 60 / 60 / 24);
    const predictionIndex = dayOfYear % predictions.length;

    return predictions[predictionIndex];
}

// دالة للحصول على التاريخ بالعربية
function getArabicDate() {
    const today = new Date();
    const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const dayName = days[today.getDay()];
    const day = today.getDate();
    const month = months[today.getMonth()];
    const year = today.getFullYear();

    return `${dayName} ${day} ${month} ${year}`;
}

// بيانات الأبراج الفلكية مع الألوان المفضلة والصفات
const zodiacSigns = {
    'الحمل': {
        dates: '21 مارس - 19 أبريل',
        color: '#DC143C',
        colorName: 'أحمر',
        positiveTraits: ['شجاع', 'قيادي', 'متحمس', 'مبادر', 'صادق', 'مستقل'],
        negativeTraits: ['متهور', 'عنيد', 'عدواني', 'أناني', 'صبره قليل', 'متسرع'],
        element: 'النار',
        planet: 'المريخ',
        gemstone: 'الياقوت الأحمر',
        symbol: '♈',
        description: 'برج ناري قيادي، يتميز بالشجاعة والحماس. أصحاب هذا البرج رواد ومبادرون، يحبون التحديات والمغامرات الجديدة.',
        compatibility: ['الأسد', 'القوس', 'الجوزاء', 'الدلو'],
        luckyNumbers: [1, 8, 17, 26],
        careers: ['قائد عسكري', 'رياضي', 'رجل أعمال', 'مدير تنفيذي', 'جراح'],
        dailyTip: 'اليوم مناسب لبدء مشاريع جديدة. ثق بحدسك واتخذ قرارات جريئة.'
    },
    'الثور': {
        dates: '20 أبريل - 20 مايو',
        color: '#228B22',
        colorName: 'أخضر',
        positiveTraits: ['صبور', 'مخلص', 'عملي', 'موثوق', 'مثابر', 'محب للجمال'],
        negativeTraits: ['عنيد', 'مادي', 'كسول', 'غيور', 'بطيء التغيير', 'متملك'],
        element: 'التراب',
        planet: 'الزهرة',
        gemstone: 'الزمرد',
        symbol: '♉',
        description: 'برج ترابي عملي، يتميز بالثبات والإخلاص. أصحاب هذا البرج يحبون الاستقرار والجمال، ويقدرون الأشياء الجميلة في الحياة.',
        compatibility: ['العذراء', 'الجدي', 'السرطان', 'الحوت'],
        luckyNumbers: [2, 6, 9, 12, 24],
        careers: ['مصمم', 'طاهي', 'مزارع', 'مصرفي', 'مهندس معماري'],
        dailyTip: 'خذ وقتك في اتخاذ القرارات اليوم. الصبر سيجلب لك النتائج المرجوة.'
    },
    'الجوزاء': {
        dates: '21 مايو - 20 يونيو',
        color: '#FFD700',
        colorName: 'أصفر',
        positiveTraits: ['ذكي', 'متكيف', 'فضولي', 'اجتماعي', 'مرح', 'متعدد المواهب'],
        negativeTraits: ['متقلب', 'سطحي', 'قلق', 'غير مستقر', 'ثرثار', 'مشتت'],
        element: 'الهواء',
        planet: 'عطارد',
        gemstone: 'العقيق',
        symbol: '♊',
        description: 'برج هوائي ذكي، يتميز بالفضول والتكيف. أصحاب هذا البرج محبون للتعلم والتواصل، ولديهم قدرة على التأقلم مع المواقف المختلفة.',
        compatibility: ['الميزان', 'الدلو', 'الحمل', 'الأسد'],
        luckyNumbers: [5, 7, 14, 23],
        careers: ['صحفي', 'مترجم', 'مذيع', 'كاتب', 'مندوب مبيعات'],
        dailyTip: 'يوم مثالي للتواصل والتعلم. استغل فضولك الطبيعي لاكتشاف أشياء جديدة.'
    },
    'السرطان': {
        dates: '21 يونيو - 22 يوليو',
        color: '#C0C0C0',
        colorName: 'فضي',
        positiveTraits: ['حنون', 'مخلص', 'حدسي', 'حامي', 'عاطفي', 'محب للعائلة'],
        negativeTraits: ['مزاجي', 'حساس جداً', 'متشائم', 'متشبث', 'خجول', 'انطوائي'],
        element: 'الماء',
        planet: 'القمر',
        gemstone: 'اللؤلؤ',
        symbol: '♋',
        description: 'برج مائي عاطفي، يتميز بالحنان والحدس القوي. أصحاب هذا البرج محبون للعائلة والبيت، ولديهم قدرة على فهم مشاعر الآخرين.',
        compatibility: ['العقرب', 'الحوت', 'الثور', 'العذراء'],
        luckyNumbers: [2, 7, 11, 16, 20, 25],
        careers: ['ممرض', 'معلم', 'طبيب نفسي', 'طاهي', 'مصمم داخلي'],
        dailyTip: 'اعتمد على حدسك اليوم. مشاعرك ستقودك للقرارات الصحيحة.'
    },
    'الأسد': {
        dates: '23 يوليو - 22 أغسطس',
        color: '#FF8C00',
        colorName: 'برتقالي',
        positiveTraits: ['واثق', 'كريم', 'مبدع', 'دافئ', 'مرح', 'قيادي'],
        negativeTraits: ['متكبر', 'أناني', 'عنيد', 'متسلط', 'مغرور', 'درامي'],
        element: 'النار',
        planet: 'الشمس',
        gemstone: 'الذهب الأصفر',
        symbol: '♌',
        description: 'برج ناري ملكي، يتميز بالثقة والكرم. أصحاب هذا البرج محبون للأضواء والإبداع، ولديهم شخصية جذابة وقيادية طبيعية.',
        compatibility: ['الحمل', 'القوس', 'الجوزاء', 'الميزان'],
        luckyNumbers: [1, 3, 10, 19],
        careers: ['ممثل', 'مدير', 'فنان', 'مصمم أزياء', 'منظم فعاليات'],
        dailyTip: 'أظهر إبداعك وثقتك اليوم. الأضواء ستسلط عليك بطريقة إيجابية.'
    },
    'العذراء': {
        dates: '23 أغسطس - 22 سبتمبر',
        color: '#4169E1',
        colorName: 'أزرق',
        positiveTraits: ['منظم', 'دقيق', 'مفيد', 'عملي', 'متواضع', 'مجتهد'],
        negativeTraits: ['انتقادي', 'قلق', 'كمالي', 'خجول', 'متشدد', 'متشائم'],
        element: 'التراب',
        planet: 'عطارد',
        gemstone: 'الياقوت الأزرق',
        symbol: '♍',
        description: 'برج ترابي دقيق، يتميز بالتنظيم والكمال. أصحاب هذا البرج محبون للتفاصيل والدقة، ولديهم رغبة في مساعدة الآخرين وتحسين الأمور.',
        compatibility: ['الثور', 'الجدي', 'السرطان', 'العقرب'],
        luckyNumbers: [6, 15, 20, 27],
        careers: ['محاسب', 'طبيب', 'محرر', 'مدقق', 'صيدلي'],
        dailyTip: 'ركز على التفاصيل اليوم. دقتك ستؤدي إلى نتائج ممتازة.'
    },
    'الميزان': {
        dates: '23 سبتمبر - 22 أكتوبر',
        color: '#FFB6C1',
        colorName: 'وردي',
        positiveTraits: ['عادل', 'دبلوماسي', 'ساحر', 'اجتماعي', 'متوازن', 'محب للسلام'],
        negativeTraits: ['متردد', 'سطحي', 'تجنبي', 'كسول', 'مترف', 'متقلب'],
        element: 'الهواء',
        planet: 'الزهرة',
        gemstone: 'الأوبال',
        symbol: '♎',
        description: 'برج هوائي متوازن، يتميز بالعدالة والدبلوماسية. أصحاب هذا البرج محبون للجمال والانسجام، ولديهم قدرة على إيجاد التوازن في العلاقات.',
        compatibility: ['الجوزاء', 'الدلو', 'الأسد', 'القوس'],
        luckyNumbers: [4, 6, 13, 15, 24],
        careers: ['محامي', 'دبلوماسي', 'مستشار', 'مصمم', 'وسيط'],
        dailyTip: 'ابحث عن التوازن في جميع جوانب حياتك اليوم. الانسجام سيجلب لك السعادة.'
    },
    'العقرب': {
        dates: '23 أكتوبر - 21 نوفمبر',
        color: '#8B0000',
        colorName: 'أحمر قاتم',
        positiveTraits: ['شجاع', 'مخلص', 'حدسي', 'عاطفي', 'مصمم', 'غامض'],
        negativeTraits: ['غيور', 'انتقامي', 'سري', 'عنيد', 'متشكك', 'متملك'],
        element: 'الماء',
        planet: 'المريخ وبلوتو',
        gemstone: 'التوباز',
        symbol: '♏',
        description: 'برج مائي قوي، يتميز بالعمق والغموض. أصحاب هذا البرج لديهم قدرة على التحول والتجديد، وحدس قوي في فهم الأمور الخفية.',
        compatibility: ['السرطان', 'الحوت', 'العذراء', 'الجدي'],
        luckyNumbers: [4, 13, 18, 22],
        careers: ['محقق', 'طبيب نفسي', 'جراح', 'باحث', 'محلل مالي'],
        dailyTip: 'ثق بحدسك القوي اليوم. الأسرار المخفية ستكشف عن نفسها لك.'
    },
    'القوس': {
        dates: '22 نوفمبر - 21 ديسمبر',
        color: '#9370DB',
        colorName: 'بنفسجي',
        positiveTraits: ['متفائل', 'مغامر', 'صادق', 'فلسفي', 'مرح', 'حر'],
        negativeTraits: ['متهور', 'غير مسؤول', 'مبالغ', 'قاسي', 'غير صبور', 'متسرع'],
        element: 'النار',
        planet: 'المشتري',
        gemstone: 'الفيروز',
        symbol: '♐',
        description: 'برج ناري مغامر، يتميز بالتفاؤل وحب الاستطلاع. أصحاب هذا البرج محبون للسفر والفلسفة، ولديهم رؤية واسعة للحياة.',
        compatibility: ['الحمل', 'الأسد', 'الميزان', 'الدلو'],
        luckyNumbers: [3, 9, 15, 21, 34],
        careers: ['مرشد سياحي', 'فيلسوف', 'مدرس', 'مترجم', 'مصور'],
        dailyTip: 'اليوم مثالي للمغامرات الجديدة. وسع آفاقك واستكشف أماكن جديدة.'
    },
    'الجدي': {
        dates: '22 ديسمبر - 19 يناير',
        color: '#8B4513',
        colorName: 'بني',
        positiveTraits: ['طموح', 'منضبط', 'مسؤول', 'صبور', 'عملي', 'حكيم'],
        negativeTraits: ['متشائم', 'عنيد', 'بارد', 'متحكم', 'مادي', 'متشدد'],
        element: 'التراب',
        planet: 'زحل',
        gemstone: 'الجارنت',
        symbol: '♑',
        description: 'برج ترابي طموح، يتميز بالانضباط والمسؤولية. أصحاب هذا البرج محبون للنجاح والإنجاز، ولديهم قدرة على التحمل والصبر.',
        compatibility: ['الثور', 'العذراء', 'العقرب', 'الحوت'],
        luckyNumbers: [8, 10, 16, 26],
        careers: ['مدير تنفيذي', 'مهندس', 'محاسب', 'سياسي', 'مقاول'],
        dailyTip: 'ركز على أهدافك طويلة المدى اليوم. الصبر والمثابرة ستحقق لك النجاح.'
    },
    'الدلو': {
        dates: '20 يناير - 18 فبراير',
        color: '#00BFFF',
        colorName: 'أزرق فاتح',
        positiveTraits: ['مبتكر', 'مستقل', 'إنساني', 'ذكي', 'أصيل', 'صديق'],
        negativeTraits: ['منعزل', 'عنيد', 'متمرد', 'غير عاطفي', 'غريب الأطوار', 'لا يمكن التنبؤ به'],
        element: 'الهواء',
        planet: 'زحل وأورانوس',
        gemstone: 'الأمثيست',
        symbol: '♒',
        description: 'برج هوائي مبتكر، يتميز بالاستقلالية والإنسانية. أصحاب هذا البرج محبون للتجديد والتقدم، ولديهم رؤية مستقبلية فريدة.',
        compatibility: ['الجوزاء', 'الميزان', 'الحمل', 'القوس'],
        luckyNumbers: [4, 7, 11, 22, 29],
        careers: ['مخترع', 'مبرمج', 'عالم', 'ناشط اجتماعي', 'مصمم تقني'],
        dailyTip: 'فكر خارج الصندوق اليوم. أفكارك المبتكرة ستجد طريقها للتطبيق.'
    },
    'الحوت': {
        dates: '19 فبراير - 20 مارس',
        color: '#008B8B',
        colorName: 'أخضر مائي',
        positiveTraits: ['حدسي', 'رحيم', 'فني', 'لطيف', 'حكيم', 'مسامح'],
        negativeTraits: ['حالم', 'ساذج', 'مزاجي', 'هارب', 'ضعيف الإرادة', 'ضحية'],
        element: 'الماء',
        planet: 'المشتري ونبتون',
        gemstone: 'الأكوامارين',
        symbol: '♓',
        description: 'برج مائي حالم، يتميز بالحدس والرحمة. أصحاب هذا البرج لديهم خيال واسع وقدرة على التعاطف، ومواهب فنية وروحية عالية.',
        compatibility: ['السرطان', 'العقرب', 'الثور', 'الجدي'],
        luckyNumbers: [3, 9, 12, 15, 18, 24],
        careers: ['فنان', 'موسيقي', 'معالج نفسي', 'ممرض', 'كاتب'],
        dailyTip: 'استمع لصوتك الداخلي اليوم. حدسك وإبداعك سيقودانك لأشياء جميلة.'
    }
};

// دالة تحديد البرج الفلكي
function getZodiacSign(month, day) {
    if ((month == 3 && day >= 21) || (month == 4 && day <= 19)) return 'الحمل';
    if ((month == 4 && day >= 20) || (month == 5 && day <= 20)) return 'الثور';
    if ((month == 5 && day >= 21) || (month == 6 && day <= 20)) return 'الجوزاء';
    if ((month == 6 && day >= 21) || (month == 7 && day <= 22)) return 'السرطان';
    if ((month == 7 && day >= 23) || (month == 8 && day <= 22)) return 'الأسد';
    if ((month == 8 && day >= 23) || (month == 9 && day <= 22)) return 'العذراء';
    if ((month == 9 && day >= 23) || (month == 10 && day <= 22)) return 'الميزان';
    if ((month == 10 && day >= 23) || (month == 11 && day <= 21)) return 'العقرب';
    if ((month == 11 && day >= 22) || (month == 12 && day <= 21)) return 'القوس';
    if ((month == 12 && day >= 22) || (month == 1 && day <= 19)) return 'الجدي';
    if ((month == 1 && day >= 20) || (month == 2 && day <= 18)) return 'الدلو';
    if ((month == 2 && day >= 19) || (month == 3 && day <= 20)) return 'الحوت';
}

// دالة حساب العمر
function calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    
    let years = today.getFullYear() - birth.getFullYear();
    let months = today.getMonth() - birth.getMonth();
    let days = today.getDate() - birth.getDate();
    
    if (days < 0) {
        months--;
        const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        days += lastMonth.getDate();
    }
    
    if (months < 0) {
        years--;
        months += 12;
    }
    
    return { years, months, days };
}

// دالة تحويل التاريخ للهجري
function convertToHijri(gregorianDate) {
    try {
        const hijriDate = moment(gregorianDate).format('iYYYY/iM/iD');
        const hijriDateFormatted = moment(gregorianDate).format('iD iMMMM iYYYY');
        return hijriDateFormatted;
    } catch (error) {
        console.error('خطأ في تحويل التاريخ الهجري:', error);
        return 'غير متاح';
    }
}

// دالة عرض النتائج
function displayResults(birthDate) {
    const age = calculateAge(birthDate);
    const birth = new Date(birthDate);
    const zodiac = getZodiacSign(birth.getMonth() + 1, birth.getDate());
    const hijriDate = convertToHijri(birthDate);
    
    // عرض العمر
    const ageDisplay = document.getElementById('age-result');
    ageDisplay.innerHTML = `
        <div class="age-unit">
            <span class="age-number">${age.years}</span>
            <span class="age-label">سنة</span>
        </div>
        <div class="age-unit">
            <span class="age-number">${age.months}</span>
            <span class="age-label">شهر</span>
        </div>
        <div class="age-unit">
            <span class="age-number">${age.days}</span>
            <span class="age-label">يوم</span>
        </div>
    `;
    
    // عرض التاريخ الهجري
    document.getElementById('hijri-result').textContent = hijriDate;
    
    // عرض البرج الفلكي
    const zodiacInfo = zodiacSigns[zodiac];
    document.getElementById('zodiac-symbol').textContent = zodiacInfo.symbol;
    document.getElementById('zodiac-name').textContent = zodiac;
    document.getElementById('zodiac-dates').textContent = zodiacInfo.dates;
    document.getElementById('zodiac-description').textContent = zodiacInfo.description;
    document.getElementById('favorite-color').textContent = zodiacInfo.colorName;
    document.getElementById('favorite-color').style.backgroundColor = zodiacInfo.color;

    // عرض معلومات البرج
    document.getElementById('zodiac-element').textContent = zodiacInfo.element;
    document.getElementById('zodiac-planet').textContent = zodiacInfo.planet;
    document.getElementById('zodiac-gemstone').textContent = zodiacInfo.gemstone;

    // تغيير لون خلفية قسم البرج
    const zodiacColorDisplay = document.querySelector('.zodiac-color-display');
    zodiacColorDisplay.style.backgroundColor = zodiacInfo.color + '20'; // شفافية 20%

    // عرض الصفات الإيجابية
    const positiveTraitsContainer = document.getElementById('positive-traits');
    positiveTraitsContainer.innerHTML = '';
    zodiacInfo.positiveTraits.forEach(trait => {
        const traitTag = document.createElement('span');
        traitTag.className = 'trait-tag';
        traitTag.textContent = trait;
        positiveTraitsContainer.appendChild(traitTag);
    });

    // عرض الصفات السلبية
    const negativeTraitsContainer = document.getElementById('negative-traits');
    negativeTraitsContainer.innerHTML = '';
    zodiacInfo.negativeTraits.forEach(trait => {
        const traitTag = document.createElement('span');
        traitTag.className = 'trait-tag';
        traitTag.textContent = trait;
        negativeTraitsContainer.appendChild(traitTag);
    });

    // عرض المهن المناسبة
    const careerListContainer = document.getElementById('career-list');
    careerListContainer.innerHTML = '';
    zodiacInfo.careers.forEach(career => {
        const careerTag = document.createElement('span');
        careerTag.className = 'career-tag';
        careerTag.textContent = career;
        careerListContainer.appendChild(careerTag);
    });

    // عرض الأرقام المحظوظة
    const numbersListContainer = document.getElementById('numbers-list');
    numbersListContainer.innerHTML = '';
    zodiacInfo.luckyNumbers.forEach(number => {
        const numberTag = document.createElement('span');
        numberTag.className = 'number-tag';
        numberTag.textContent = number;
        numbersListContainer.appendChild(numberTag);
    });

    // عرض التوافق مع الأبراج
    const compatibilityContainer = document.getElementById('compatible-signs');
    compatibilityContainer.innerHTML = '';
    zodiacInfo.compatibility.forEach(sign => {
        const compatibilityTag = document.createElement('span');
        compatibilityTag.className = 'compatibility-tag';
        compatibilityTag.textContent = sign;
        compatibilityContainer.appendChild(compatibilityTag);
    });

    // عرض نصيحة اليوم
    const dailyTipContainer = document.getElementById('daily-tip-text');
    dailyTipContainer.textContent = zodiacInfo.dailyTip;

    // عرض التوقعات اليومية
    const predictionDateContainer = document.getElementById('prediction-date');
    const dailyPredictionContainer = document.getElementById('daily-prediction-text');

    predictionDateContainer.textContent = getArabicDate();
    dailyPredictionContainer.textContent = getDailyPrediction(zodiac);

    // تغيير لون خلفية كامل النتائج
    const resultCard = document.querySelector('.result-card');
    resultCard.style.background = `linear-gradient(135deg, rgba(255,255,255,0.95) 0%, ${zodiacInfo.color}20 100%)`;
    
    // إظهار النتائج
    document.getElementById('results').classList.remove('hidden');
    
    // التمرير للنتائج
    document.getElementById('results').scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
}

// دالة تحديث التوقع
function refreshPrediction() {
    const zodiacNameElement = document.getElementById('zodiac-name');
    if (!zodiacNameElement || !zodiacNameElement.textContent) {
        alert('⚠️ يرجى حساب برجك أولاً');
        return;
    }

    const zodiacSign = zodiacNameElement.textContent;
    const predictions = dailyPredictions[zodiacSign];

    if (predictions) {
        // اختيار توقع عشوائي
        const randomIndex = Math.floor(Math.random() * predictions.length);
        const newPrediction = predictions[randomIndex];

        const dailyPredictionContainer = document.getElementById('daily-prediction-text');

        // تأثير بصري للتحديث
        dailyPredictionContainer.style.opacity = '0.5';
        dailyPredictionContainer.style.transform = 'scale(0.95)';

        setTimeout(() => {
            dailyPredictionContainer.textContent = newPrediction;
            dailyPredictionContainer.style.opacity = '1';
            dailyPredictionContainer.style.transform = 'scale(1)';
        }, 300);
    }
}

// إعداد الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    const birthdateInput = document.getElementById('birthdate');

    calculateBtn.addEventListener('click', function() {
        const birthDate = birthdateInput.value;

        if (!birthDate) {
            alert('⚠️ يرجى إدخال تاريخ الميلاد أولاً');
            return;
        }

        const selectedDate = new Date(birthDate);
        const today = new Date();

        if (selectedDate > today) {
            alert('⚠️ تاريخ الميلاد لا يمكن أن يكون في المستقبل');
            return;
        }

        displayResults(birthDate);
    });

    // إضافة حدث الضغط على Enter
    birthdateInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            calculateBtn.click();
        }
    });

    // إضافة حدث تحديث التوقع
    document.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'refresh-prediction') {
            refreshPrediction();
        }
    });
});

// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي
    const container = document.querySelector('.container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        container.style.transition = 'all 0.8s ease';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);
});
