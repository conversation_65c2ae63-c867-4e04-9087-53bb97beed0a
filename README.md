# 🌟 حاسبة العمر والبرج الفلكي 🌟

تطبيق ويب تفاعلي باللغة العربية لحساب العمر والبرج الفلكي مع التحويل للتاريخ الهجري.

## ✨ المميزات

- 🎂 **حساب العمر الدقيق**: يحسب العمر بالسنوات والأشهر والأيام
- 🌙 **التحويل للتاريخ الهجري**: تحويل تاريخ الميلاد للتقويم الهجري
- ⭐ **تحديد البرج الفلكي**: معرفة البرج الفلكي مع اللون المفضل
- 🎨 **تصميم أنيق**: واجهة جميلة مع ألوان متدرجة
- 📱 **متجاوب**: يعمل على جميع الأجهزة والشاشات
- 🔄 **دعم RTL**: مصمم خصيصاً للغة العربية

## 🚀 كيفية الاستخدام

1. افتح ملف `index.html` في المتصفح
2. أدخل تاريخ ميلادك في الحقل المخصص
3. اضغط على زر "احسب الآن"
4. استمتع بمشاهدة النتائج الملونة!

## 📁 ملفات المشروع

- `index.html` - الهيكل الأساسي للتطبيق
- `style.css` - التصميم والألوان
- `script.js` - الوظائف والحسابات
- `README.md` - دليل الاستخدام

## 🎨 ألوان الأبراج

| البرج | اللون المفضل |
|-------|-------------|
| الحمل | أحمر |
| الثور | أخضر |
| الجوزاء | أصفر |
| السرطان | فضي |
| الأسد | برتقالي |
| العذراء | أزرق |
| الميزان | وردي |
| العقرب | أحمر قاتم |
| القوس | بنفسجي |
| الجدي | بني |
| الدلو | أزرق فاتح |
| الحوت | أخضر مائي |

## 🛠️ التقنيات المستخدمة

- **HTML5**: للهيكل الأساسي
- **CSS3**: للتصميم والتنسيق
- **JavaScript**: للوظائف والحسابات
- **Moment.js**: لتحويل التاريخ الهجري
- **Google Fonts**: خط Cairo العربي

## 📋 المتطلبات

- متصفح ويب حديث يدعم HTML5 و CSS3 و JavaScript
- اتصال بالإنترنت (لتحميل الخطوط والمكتبات)

## 🎯 المميزات التقنية

- **Front-End Only**: لا يحتاج لخادم أو قاعدة بيانات
- **Responsive Design**: متجاوب مع جميع أحجام الشاشات
- **RTL Support**: دعم كامل للاتجاه من اليمين لليسار
- **Modern CSS**: استخدام Flexbox و Grid
- **Smooth Animations**: تأثيرات بصرية ناعمة

## 🔧 التخصيص

يمكنك تخصيص التطبيق عبر:

1. **تغيير الألوان**: في ملف `style.css`
2. **إضافة أبراج جديدة**: في ملف `script.js`
3. **تعديل التصميم**: في ملف `style.css`
4. **إضافة وظائف جديدة**: في ملف `script.js`

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى التواصل معنا.

## 👨‍💻 المطور

**محمد طعم شريف الخفاجي**

---

💫 **تم التطوير بحب للثقافة العربية** 💫
