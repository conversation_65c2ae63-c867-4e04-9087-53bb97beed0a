# إنشاء ملف APK تجريبي لتطبيق حاسبة الأبراج
# المطور: محمد طعم شريف الخفاجي

Write-Host "📱 إنشاء ملف APK تجريبي لحاسبة الأبراج" -ForegroundColor Cyan
Write-Host "=========================================="

# إنشاء مجلد APK_OUTPUT
$outputDir = "APK_OUTPUT"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir | Out-Null
}

# إنشاء ملف معلومات التطبيق
$infoFile = "$outputDir\معلومات-التطبيق.txt"
@"
🌟 تطبيق حاسبة الأبراج 🌟
============================

📱 معلومات التطبيق:
   الاسم: حاسبة الأبراج
   المعرف: com.mahm.zodiac.calculator
   الإصدار: 1.0.0
   المطور: محمد طعم شريف الخفاجي

📅 تاريخ الإنشاء: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

🤖 متطلبات النظام:
   أندرويد: 5.1+ (API 22)
   المساحة المطلوبة: 15 MB
   الإنترنت: مطلوب للتوقعات اليومية

📋 طريقة التثبيت:
   1. فعّل "مصادر غير معروفة" في إعدادات الأمان
   2. اضغط على ملف APK
   3. اتبع تعليمات التثبيت

✨ المميزات الرئيسية:
   🎂 حساب العمر الدقيق بالسنوات والأشهر والأيام
   🌙 تحويل تاريخ الميلاد للتقويم الهجري
   ⭐ معلومات شاملة عن جميع الأبراج الـ12
   🔮 أكثر من 180 توقع يومي متجدد
   💼 اقتراحات المهن المناسبة لكل برج
   🍀 الأرقام المحظوظة لكل برج
   💕 توافق الأبراج مع بعضها البعض
   💡 نصائح يومية مخصصة

🎨 مميزات التصميم:
   - واجهة عربية أصيلة مع دعم RTL
   - ألوان متدرجة جميلة (أزرق → بنفسجي)
   - تصميم متجاوب لجميع أحجام الشاشات
   - خطوط عربية أنيقة (Cairo)
   - تأثيرات بصرية متقدمة

🔒 الأمان والخصوصية:
   - التطبيق آمن 100%
   - لا يجمع بيانات شخصية
   - لا يحتوي على إعلانات
   - مفتوح المصدر

📞 الدعم التقني:
   في حالة مواجهة أي مشاكل، تأكد من:
   - إصدار أندرويد 5.1 أو أحدث
   - توفر مساحة كافية (15 MB)
   - تفعيل "مصادر غير معروفة"

🎉 استمتع بالتطبيق!

---
👨‍💻 المطور: محمد طعم شريف الخفاجي
📅 تاريخ الإنشاء: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
🌟 الإصدار: 1.0.0

ملاحظة: هذا ملف تجريبي. لإنشاء ملف APK حقيقي، يرجى:
1. تثبيت Android Studio
2. تثبيت Cordova: npm install -g cordova
3. تشغيل: cordova build android
"@ | Out-File -FilePath $infoFile -Encoding UTF8

# إنشاء ملف README
$readmeFile = "$outputDir\README.md"
@"
# 📱 ملفات APK - تطبيق حاسبة الأبراج

## 📁 محتويات المجلد

### 📄 ملفات إضافية:
- `معلومات-التطبيق.txt` - معلومات مفصلة عن التطبيق
- `README.md` - هذا الملف

## 🚀 طريقة الحصول على ملف APK

### المتطلبات:
1. **Node.js** (14.0+)
2. **Apache Cordova** (12.0+)
3. **Java JDK** (8+)
4. **Android SDK** (API 22-33)

### خطوات البناء:
```bash
# 1. تثبيت Cordova
npm install -g cordova

# 2. تثبيت التبعيات
npm install

# 3. إضافة منصة أندرويد
cordova platform add android

# 4. بناء التطبيق
cordova build android

# 5. النتيجة
# المسار: platforms/android/app/build/outputs/apk/debug/app-debug.apk
```

## 📊 معلومات التطبيق

- **الاسم**: حاسبة الأبراج
- **المعرف**: com.mahm.zodiac.calculator
- **الإصدار**: 1.0.0
- **الحجم المتوقع**: ~8-12 MB
- **أندرويد**: 5.1+ (API 22)
- **المطور**: محمد طعم شريف الخفاجي

## 🌟 المميزات

- 🎂 حساب العمر الدقيق
- 🌙 التحويل الهجري
- ⭐ معلومات الأبراج الشاملة
- 🔮 180+ توقع يومي
- 💼 المهن المناسبة
- 🍀 الأرقام المحظوظة
- 💕 توافق الأبراج

## 🛠️ حل المشاكل

### إذا فشل البناء:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من متغير ANDROID_HOME
3. جرب: cordova clean android
4. أعد المحاولة: cordova build android

---

📅 تاريخ الإنشاء: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
👨‍💻 المطور: محمد طعم شريف الخفاجي
"@ | Out-File -FilePath $readmeFile -Encoding UTF8

# إنشاء ملف دليل البناء
$buildGuideFile = "$outputDir\دليل-البناء.txt"
@"
📱 دليل بناء تطبيق حاسبة الأبراج
===============================

🎯 الهدف: الحصول على ملف APK جاهز للتثبيت

📋 المتطلبات الأساسية:
1. Node.js (تحميل من: https://nodejs.org/)
2. Java JDK 8+ (تحميل من: https://www.oracle.com/java/)
3. Android Studio (تحميل من: https://developer.android.com/studio)

🚀 خطوات البناء:

الخطوة 1: تثبيت Cordova
------------------------
npm install -g cordova

الخطوة 2: تثبيت التبعيات
-----------------------
npm install

الخطوة 3: إضافة منصة أندرويد
---------------------------
cordova platform add android

الخطوة 4: إضافة الإضافات
-----------------------
cordova plugin add cordova-plugin-whitelist
cordova plugin add cordova-plugin-statusbar
cordova plugin add cordova-plugin-device
cordova plugin add cordova-plugin-splashscreen
cordova plugin add cordova-plugin-network-information
cordova plugin add cordova-plugin-vibration
cordova plugin add cordova-plugin-dialogs

الخطوة 5: بناء التطبيق
--------------------
cordova build android

📁 النتيجة:
ملف APK في: platforms\android\app\build\outputs\apk\debug\app-debug.apk

🎉 تهانينا! ملف APK جاهز للتثبيت

📱 طريقة التثبيت:
1. انقل ملف APK إلى جهاز أندرويد
2. فعّل "مصادر غير معروفة" في الإعدادات
3. اضغط على ملف APK لتثبيته

أو استخدم ADB:
adb install app-debug.apk

---
👨‍💻 المطور: محمد طعم شريف الخفاجي
📅 التاريخ: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@ | Out-File -FilePath $buildGuideFile -Encoding UTF8

Write-Host "✅ تم إنشاء ملفات المعلومات في مجلد: $outputDir" -ForegroundColor Green
Write-Host ""
Write-Host "📁 الملفات المنشأة:" -ForegroundColor Cyan
Write-Host "   📄 معلومات-التطبيق.txt" -ForegroundColor White
Write-Host "   📖 README.md" -ForegroundColor White
Write-Host "   📋 دليل-البناء.txt" -ForegroundColor White
Write-Host ""
Write-Host "💡 لإنشاء ملف APK حقيقي:" -ForegroundColor Yellow
Write-Host "   1. ثبت Android Studio" -ForegroundColor White
Write-Host "   2. ثبت Cordova: npm install -g cordova" -ForegroundColor White
Write-Host "   3. شغل: cordova build android" -ForegroundColor White
Write-Host ""
Write-Host "🎉 ملفات المعلومات جاهزة!" -ForegroundColor Green
