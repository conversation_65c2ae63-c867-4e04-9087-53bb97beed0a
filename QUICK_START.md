# 🚀 دليل البدء السريع - تطبيق حاسبة الأبراج

## ⚡ البدء السريع (5 دقائق)

### 1. التحضير
```bash
# تأكد من تثبيت Node.js
node --version

# تثبيت Cordova
npm install -g cordova
```

### 2. البناء السريع
```bash
# منح الصلاحيات
chmod +x build.sh

# بناء التطبيق
./build.sh
```

### 3. النتيجة
```
✅ ملف APK جاهز في: platforms/android/app/build/outputs/apk/debug/app-debug.apk
```

## 📱 تثبيت التطبيق

### على الجهاز مباشرة:
1. انقل ملف APK للجهاز
2. فعّل "مصادر غير معروفة" في الإعدادات
3. اضغط على الملف لتثبيته

### باستخدام ADB:
```bash
adb install app-debug.apk
```

## 🎯 مميزات التطبيق

- 🎂 حساب العمر الدقيق
- 🌙 التحويل للتاريخ الهجري  
- ⭐ معلومات الأبراج الشاملة
- 🔮 180+ توقع يومي
- 💼 المهن المناسبة
- 🍀 الأرقام المحظوظة
- 💕 توافق الأبراج
- 📱 تصميم متجاوب

## 🔧 إعدادات سريعة

### تغيير اسم التطبيق:
```xml
<!-- في config.xml -->
<name>الاسم الجديد</name>
```

### تغيير الأيقونة:
```bash
# ضع الأيقونة في res/icon/android/
# بأحجام: 36x36, 48x48, 72x72, 96x96, 144x144, 192x192
```

## 🆘 حل المشاكل السريع

### مشكلة: Command not found
```bash
# تثبيت Cordova
npm install -g cordova
```

### مشكلة: ANDROID_HOME
```bash
# إضافة للـ PATH
export ANDROID_HOME=/path/to/android-sdk
```

### مشكلة: Build failed
```bash
# تنظيف وإعادة البناء
cordova clean android
cordova build android
```

## 📊 معلومات التطبيق

- **الحجم**: ~8 MB
- **أندرويد**: 5.1+ (API 22)
- **اللغة**: العربية (RTL)
- **المطور**: محمد طعم شريف الخفاجي

## 🎉 تهانينا!

تطبيقك جاهز للاستخدام! 🎊

للمزيد من التفاصيل، راجع ملف `BUILD_INSTRUCTIONS.md`
